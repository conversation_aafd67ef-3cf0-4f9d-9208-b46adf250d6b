<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - EcoRide</title>

    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="{{ asset('css/partials/_dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>

<body>
    @include('partials.header')

    <div class="dashboard-container">
        <h1 class="dashboard-title">Dashboard Administrateur</h1>

        @if (session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_users'] }}</div>
                <div class="stat-label">Utilisateurs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_vehicles'] }}</div>
                <div class="stat-label">Véhicules</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_trips'] }}</div>
                <div class="stat-label">Covoiturages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['active_trips'] }}</div>
                <div class="stat-label">Trajets actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['completed_trips'] }}</div>
                <div class="stat-label">Trajets terminés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['cancelled_trips'] }}</div>
                <div class="stat-label">Trajets annulés</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Gestion des utilisateurs -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-users"></i> Gestion des Utilisateurs</h2>
                </div>
                <div class="widget-content">
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Rôle</th>
                                    <th>Crédits</th>
                                    <th>Inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($users as $user)
                                    <tr>
                                        <td>{{ $user->id }}</td>
                                        <td>{{ $user->name }}</td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            <select onchange="changeUserRole({{ $user->id }}, this.value)"
                                                class="form-select">
                                                <option value="passager"
                                                    {{ $user->role === 'passager' ? 'selected' : '' }}>Passager
                                                </option>
                                                <option value="conducteur"
                                                    {{ $user->role === 'conducteur' ? 'selected' : '' }}>Conducteur
                                                </option>
                                                <option value="les_deux"
                                                    {{ $user->role === 'les_deux' ? 'selected' : '' }}>Les deux
                                                </option>
                                                <option value="admin" {{ $user->role === 'admin' ? 'selected' : '' }}>
                                                    Admin</option>
                                                <option value="employe"
                                                    {{ $user->role === 'employe' ? 'selected' : '' }}>Employé</option>
                                            </select>
                                        </td>
                                        <td>{{ $user->credits ?? 0 }}€</td>
                                        <td>{{ $user->created_at->format('d/m/Y') }}</td>
                                        <td>
                                            <button class="btn-base btn-danger"
                                                onclick="deleteUser({{ $user->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    {{ $users->links() }}
                </div>
            </div>

            <!-- Gestion des véhicules -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-car"></i> Gestion des Véhicules</h2>
                </div>
                <div class="widget-content">
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Propriétaire</th>
                                    <th>Marque/Modèle</th>
                                    <th>Immatriculation</th>
                                    <th>Places</th>
                                    <th>Énergie</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($vehicles as $vehicle)
                                    <tr>
                                        <td>{{ $vehicle->id }}</td>
                                        <td>{{ $vehicle->user->name ?? 'N/A' }}</td>
                                        <td>{{ $vehicle->marque }} {{ $vehicle->modele }}</td>
                                        <td>{{ $vehicle->immatriculation }}</td>
                                        <td>{{ $vehicle->nb_places }}</td>
                                        <td>
                                            <span
                                                class="status-badge {{ $vehicle->is_ecologique ? 'status-active' : 'status-inactive' }}">
                                                {{ $vehicle->energie }}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn-base btn-danger"
                                                onclick="deleteVehicleAdmin({{ $vehicle->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    {{ $vehicles->links() }}
                </div>
            </div>

            <!-- Gestion des covoiturages -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-route"></i> Gestion des Covoiturages</h2>
                </div>
                <div class="widget-content">
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Conducteur</th>
                                    <th>Trajet</th>
                                    <th>Date</th>
                                    <th>Places</th>
                                    <th>Prix</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($trips as $trip)
                                    <tr>
                                        <td>{{ $trip->id }}</td>
                                        <td>{{ $trip->conducteur->name ?? 'N/A' }}</td>
                                        <td>{{ $trip->lieu_depart }} → {{ $trip->lieu_arrivee }}</td>
                                        <td>{{ $trip->date_depart->format('d/m/Y') }}</td>
                                        <td>{{ $trip->places_disponibles }}/{{ $trip->places_totales }}</td>
                                        <td>{{ $trip->prix }}€</td>
                                        <td>
                                            <span class="status-badge status-{{ $trip->statut }}">
                                                {{ $trip->statut_formate }}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn-base btn-danger"
                                                onclick="deleteTrip({{ $trip->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    {{ $trips->links() }}
                </div>
            </div>

            <!-- Activité récente -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-clock"></i> Activité Récente</h2>
                </div>
                <div class="widget-content">
                    <h4>Nouveaux utilisateurs</h4>
                    <div class="recent-activity">
                        @foreach ($recentUsers as $user)
                            <div class="activity-item">
                                <i class="fas fa-user-plus"></i>
                                <span>{{ $user->name }} s'est inscrit</span>
                                <small>{{ $user->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    </div>

                    <h4 style="margin-top: 2rem;">Nouveaux covoiturages</h4>
                    <div class="recent-activity">
                        @foreach ($recentTrips as $trip)
                            <div class="activity-item">
                                <i class="fas fa-route"></i>
                                <span>{{ $trip->conducteur->name ?? 'N/A' }} a créé un trajet {{ $trip->lieu_depart }}
                                    → {{ $trip->lieu_arrivee }}</span>
                                <small>{{ $trip->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('partials.footer')

    <script src="{{ asset('js/dashboard.js') }}"></script>
</body>

</html>
