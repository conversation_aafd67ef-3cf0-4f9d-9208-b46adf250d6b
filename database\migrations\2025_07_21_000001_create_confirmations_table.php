<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('confirmations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('covoiturage_id')->constrained('covoiturages')->onDelete('cascade');
            $table->foreignId('passager_id')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            // Index pour améliorer les performances
            $table->index(['covoiturage_id', 'passager_id']);
            
            // Contrainte unique pour éviter les doublons
            $table->unique(['covoiturage_id', 'passager_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('confirmations');
    }
};
