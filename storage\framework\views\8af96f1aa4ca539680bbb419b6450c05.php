<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <?php
        use Illuminate\Support\Facades\Auth;
    ?>

    <title><?php echo e(config('app.name', 'EcoRide')); ?></title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body>
    <!-- HEADER -->
    <header>
        <nav class="navbar">
            <div class="logo">
                <a href="<?php echo e(route('welcome')); ?>">EcoRide</a>
            </div>
            <div class="burger" id="burger">
                <div></div>
                <div></div>
                <div></div>
            </div>
            <ul class="nav-links">
                <li><a href="<?php echo e(route('welcome')); ?>">Accueil</a></li>
                <li><a href="<?php echo e(route('trips.index')); ?>">Covoiturage</a></li>
                <li><a href="<?php echo e(route('contact')); ?>">Contact</a></li>

                <?php if(Auth::guard('admin')->check()): ?>
                    <li>
                        <a href="<?php echo e(route('dashboard_admin')); ?>" class="user-nom">
                            ADMIN
                        </a>
                    </li>
                <?php elseif(Auth::guard('employe')->check()): ?>
                    <li>
                        <a href="<?php echo e(route('dashboard_employe')); ?>" class="user-nom">
                            <?php echo e(Auth::guard('employe')->user()->name); ?>

                        </a>
                    </li>
                <?php elseif(Auth::guard('web')->check()): ?>
                    <li>
                        <a href="<?php echo e(route('dashboard_users')); ?>" class="user-nom">
                            <?php echo e(Auth::guard('web')->user()->pseudo); ?>

                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Auth::guard('admin')->check() || Auth::guard('employe')->check() || Auth::guard('web')->check()): ?>
                    <li>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="cta-button">Déconnexion</button>
                        </form>
                    </li>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="cta-button">Connexion</a>
                <?php endif; ?>
            </ul>
        </nav>

        <div class="mobile-menu" id="mobile-menu">
            <a href="<?php echo e(route('welcome')); ?>" class="cta-button">Accueil</a>
            <a href="<?php echo e(route('trips.index')); ?>" class="cta-button">Covoiturage</a>
            <a href="<?php echo e(route('contact')); ?>" class="cta-button">Contact</a>

            <?php if(Auth::guard('admin')->check()): ?>
                <a href="<?php echo e(route('dashboard_admin')); ?>" class="cta-button user-identifier">ADMIN</a>
            <?php elseif(Auth::guard('employe')->check()): ?>
                <a href="<?php echo e(route('dashboard_employe')); ?>" class="cta-button user-identifier">
                    <?php echo e(Auth::guard('employe')->user()->name); ?>

                </a>
            <?php elseif(Auth::guard('web')->check()): ?>
                <a href="<?php echo e(route('dashboard_users')); ?>"
                    class="cta-button user-identifier"><?php echo e(Auth::guard('web')->user()->pseudo); ?></a>
            <?php endif; ?>

            <?php if(Auth::guard('admin')->check() || Auth::guard('employe')->check() || Auth::guard('web')->check()): ?>
                <form method="POST" action="<?php echo e(route('logout')); ?>" class="mobile-logout-form">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="cta-button">Déconnexion</button>
                </form>
            <?php else: ?>
                <a href="<?php echo e(route('login')); ?>" class="cta-button">Connexion</a>
            <?php endif; ?>

            <div class="close-menu" id="close-menu">&times;</div>
        </div>
    </header>

    <!-- MAIN QUI CHANGE -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- FOOTER -->
    <footer>
        <div class="image-banner">
            <img src="<?php echo e(asset('images/pexels-cottonbro-5329298.jpg')); ?>" alt="Covoiturage EcoRide" class="main-image">
        </div>
        <div class="footer footer-content">
            <p class="copyright">&copy; <?php echo e(date('Y')); ?> EcoRide</p>
            <nav class="footer-nav">
                <a href="<?php echo e(route('mentions-legales')); ?>">Mentions légales</a>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </nav>
        </div>
    </footer>

    <script src="https://hammerjs.github.io/dist/hammer.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <script src="<?php echo e(asset('js/main.js')); ?>"></script>
    <script src="<?php echo e(asset('js/navbar.js')); ?>"></script>
    <script src="<?php echo e(asset('js/forms.js')); ?>"></script>
    <script src="<?php echo e(asset('js/reviews-slider.js')); ?>"></script>
    <script src="<?php echo e(asset('js/covoiturage.js')); ?>"></script>
    <?php echo $__env->yieldContent('scripts'); ?>
</body>

</html><?php /**PATH /var/www/html/resources/views/layouts/app.blade.php ENDPATH**/ ?>