/* Header ////////////////////////////////////*/


/* Header et Navigation */
.navbar {
    position: fixed;
    width: 100%;
    max-width: 100%;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    top: 0;
    left: 0;
    box-sizing: border-box;
}

.logo a {
    color: #2ecc71;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
}

.nav-links {
    display: none;
}

.nav-links a {
    color: #2c3e50;
    text-decoration: none;
    padding: 0.5rem 1rem;
    position: relative;
}

/* Trait vert au survol =>cible tous les liens dans nav-link (sauf bouton connexion) */
.nav-links a:not(.cta-button)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #2ecc71;
    transition: all 0.3s ease;
}

.nav-links a:not(.cta-button):hover::after {
    width: 80%;
    left: 10%;
}

/* bouton connexion de la navbar*/
.nav-links .cta-button {
    color: white;
}

/* Menu Burger */
.burger {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.burger div {
    width: 25px;
    height: 3px;
    background-color: #2c3e50;
    margin: 4px;
    transition: 0.3s;
}

/* Menu Mobile */
.mobile-menu {
    position: fixed;
    top: 0;
    left: -100%;
    height: 380px;
    background-color: #2ecc71;
    transition: left 0.3s ease-in-out;
    padding-top: 80px;
    z-index: 100;
    border-radius: 0 0 10px 0;
    box-shadow: 0 2px 10px 6px rgba(0, 0, 0, 0.3);
}

.mobile-menu a {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    transition: 0.3s;
}

.mobile-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.close-menu {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2rem;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}


.close-menu:hover {
    transform: translateX(-50%) scale(1.2);
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.mobile-menu.active {
    left: 0;
}

/* "Utilisateur" après connexion  */
.user-nom {
    color: inherit;
    text-decoration: none;
    cursor: pointer;
    font-weight: bold;
}


.user-identifier {
    font-weight: bold;
}

.mobile-logout-form {
    text-align: center;
    margin-top: 10px;
}

/* main /////////////////////////////////////*/
main {
    padding-top: 80px; /* Hauteur approximative du menu desktop fixe */
}


/* Footer ///////////////////////////////////*/
footer {
    background-color: #2c3e50;
    color: white;
    padding-bottom: 2rem;
    text-align: center;
    margin-top: auto;
    width: 100%;
}

footer a {
    color: #2ecc71;
    text-decoration: none;
}

.footer-nav {
    display: flex;
    padding: 0 1rem;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.footer-nav a[href^="mailto:"] {
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.image-banner {
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
}

.image-banner img {
    width: 100%;
    height: 300px;
    display: block;
    object-fit: cover;
    margin-bottom: 2rem;
}

/* Version ordi */
@media (min-width: 768px) {
    .burger {
        display: none;
    }

    .nav-links {
        display: flex;
        gap: 20px;
        list-style: none;
        align-items: center;
    }

    .footer-nav {
        justify-content: space-between;
        flex-direction: row;
        gap: 0px;
        margin-top: 0px;
    }
}

.hidden {
    display: none !important;
}
