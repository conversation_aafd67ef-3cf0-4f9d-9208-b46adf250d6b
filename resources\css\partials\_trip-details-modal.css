/* Détails du covoiturage */
.modal {
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
}

.modal-content {
    width: 100%;
    max-width: 800px;
    margin: 30px auto;
    position: relative;
    overflow: hidden;
}

.trip-details-modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    background-color: #f8f9fa;
}

.modal-header h3 {
    font-size: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fa;
    display: flex;
    justify-content: center;
}



/* Sections de la modale */
.trip-details-section,
.driver-details-section,
.reviews-section {
    margin-bottom: 3rem;
}

.trip-details-section h4,
.driver-details-section h4,
.reviews-section h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.3rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.3rem;
    text-align: center;
}



/* Traj<PERSON> et dates */
.trip-route-container-flex {
    display: flex;
    justify-content: space-between;
}

.trip-route-container {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.trip-route-details {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bolder;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.trip-route-details .from,
.trip-route-details .to {
    padding: 5px 10px;
}

.trip-route-details .route-arrow-details {
    margin: 0 10px;
    color: #666;
    font-size: 1.5rem;
}

.trip-details-date {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 500;
    color: #2c3e50;
    text-align: left;
    margin-bottom: 0.5rem;
}

.trip-details-date i {
    margin-right: 5px;
    color: #28a745;
}



/* Horaires */
.departure-time,
.arrival-time {
    display: flex;
    align-items: center;
    color: #666;
}

.departure-time i,
.arrival-time i {
    margin-right: 5px;
    color: #28a745;
}

#modal-departure-time,
#modal-arrival-time {
    margin-left: 1rem;
}



/* Adresses */
.trip-addresses {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.trip-addresses .address-group:nth-child(2) {
    text-align: end;
}

.address-group h5 {
    margin-top: 0;
    margin-bottom: 0.6rem;
    color: #555;
    font-size: 1rem;
}

.address-group p {
    margin: 0.3rem 0;
    color: #666;
}



/* Prix */
.trip-price-container {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.trip-price-container h5 {
    margin-top: 0;
    margin-bottom: 0.8rem;
    color: #555;
    font-size: 1.1rem;
}

.trip-price-container p {
    margin: 0.3rem 0;
    color: #28a745;
    font-size: 1.2rem;
    font-weight: bold;
}



/* Info */
.trip-grid-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.trip-info-item {
    padding: 0.6rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.trip-info-item h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #555;
    font-size: 1rem;
}

.trip-info-item p {
    margin: 5px 0;
    color: #666;
    font-weight: bold;
}




/* Badge éco */
.eco-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: bold;
}

.eco-badge.eco {
    background-color: #d4edda;
    color: #155724;
}

.eco-badge.standard {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* driver-profile */
.driver-profile {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.driver-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.driver-info {
    margin-top: 1rem;
}

.driver-info h5 {
    margin-top: 0;
    margin-bottom: 0.3rem;
    color: #333;
    font-size: 1.1rem;
    text-align: center;
}

/* Véhicule et préférences */
.driver-details-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.driver-preferences,
.details-vehicle {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.driver-preferences h5,
.details-vehicle h5 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #555;
    font-size: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.3rem;
}

.preference-label {
    font-weight: bold;
    margin-right: 5px;
    color: #555;
    min-width: 100px;
}

.preference-value {
    color: #666;
}

.vehicle-info {
    display: grid;
    gap: 0.6rem;
}

.vehicle-info p {
    margin: 0;
    color: #666;
}

.vehicle-label-details {
    font-weight: bold;
    color: #555;
    margin-right: 5px;
}

.preferences-list-details{
    display: grid;
    gap: 0.6rem;
}

#modal-pref-libre-container {
    flex-direction: column;
}








/* Avis */
.reviews-container {
    position: relative;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.reviews-carousel {
    display: flex;
    align-items: center;
}

.reviews-list {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 10px 0;
    gap: 15px;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f8f9fa;
}

.carousel-arrow {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.carousel-arrow:disabled {
    color: #ccc;
    cursor: default;
}

.carousel-arrow.prev-arrow {
    margin-right: 10px;
}

.carousel-arrow.next-arrow {
    margin-left: 10px;
}

/* Card des avis */
.review-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    min-width: 250px;
    max-width: 300px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.review-author {
    font-weight: bold;
    color: #333;
}

.review-date {
    color: #999;
    font-size: 0.9rem;
}

.review-rating {
    margin-bottom: 10px;
}

.review-content {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

.no-reviews {
    padding: 20px;
    text-align: center;
    color: #999;
    font-style: italic;
    width: 100%;
}

.loading,
.error {
    padding: 20px;
    text-align: center;
    color: #999;
    width: 100%;
}

.error {
    color: #dc3545;
}

/* Responsive */
@media (max-width: 768px) {
    .trip-grid-details,
    .driver-details-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 10px;
    }

    .modal {
        padding: 10px;
    }

    .review-card {
        min-width: 200px;
    }

    .details-vehicle, .driver-preferences {
        text-align: center;
    }


}


@media (max-width: 540px) {
    .trip-route-container-flex {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .trip-route-details {
        flex-direction: column;
    }

    .route-arrow-details {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .trip-addresses {
        display: flex;
        gap: 20px;
        flex-direction: column;
        align-items: center;
    }

    h1, h2, .address-group, .trip-addresses .address-group:nth-child(2) {
        text-align: center;
    }

    .address-group {
        flex-direction: column;
        align-items: center;
    }
}
