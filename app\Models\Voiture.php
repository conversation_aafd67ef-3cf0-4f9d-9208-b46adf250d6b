<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Voiture extends Model
{
    use HasFactory;

    protected $table = 'voiture';
    protected $primaryKey = 'voiture_id';

    protected $fillable = [
        'user_id',
        'immat',
        'date_first_immat',
        'brand',
        'model',
        'color',
        'n_place',
        'energie',
    ];

    public $timestamps = false;

    protected $casts = [
        'n_place' => 'integer',
        'date_first_immat' => 'date',
    ];

    // Accesseurs pour compatibilité avec les dashboards
    public function getMarqueAttribute()
    {
        return $this->brand;
    }

    public function setMarqueAttribute($value)
    {
        $this->brand = $value;
    }

    public function getModeleAttribute()
    {
        return $this->model;
    }

    public function setModeleAttribute($value)
    {
        $this->model = $value;
    }

    public function getImmatriculationAttribute()
    {
        return $this->immat;
    }

    public function setImmatriculationAttribute($value)
    {
        $this->immat = $value;
    }

    public function getCouleurAttribute()
    {
        return $this->color;
    }

    public function setCouleurAttribute($value)
    {
        $this->color = $value;
    }

    public function getNbPlacesAttribute()
    {
        return $this->n_place;
    }

    public function setNbPlacesAttribute($value)
    {
        $this->n_place = $value;
    }

    public function getAnneeAttribute()
    {
        return $this->date_first_immat ? $this->date_first_immat->year : null;
    }

    public function setAnneeAttribute($value)
    {
        $this->date_first_immat = $value . '-01-01';
    }

    public function getIsEcologiqueAttribute()
    {
        return in_array($this->energie, ['Electrique', 'Hybride']);
    }

    public function getFullNameAttribute()
    {
        return $this->brand . ' ' . $this->model;
    }

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function covoiturages()
    {
        return $this->hasMany(Covoiturage::class, 'voiture_id', 'voiture_id');
    }
}
