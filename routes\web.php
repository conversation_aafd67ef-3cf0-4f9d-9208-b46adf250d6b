<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\VehicleController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

Route::post('/rechercher', [TripController::class, 'search'])->name('search.covoiturage');

Route::get('/covoiturages/{id}/confirmer', [TripController::class, 'confirm'])->name('trips.confirm');
Route::get('/covoiturages/{id}/participer', [TripController::class, 'participate'])->name('trips.participate');
Route::get('/covoiturages/{id}', [TripController::class, 'show'])->name('trips.show');

Route::get('/covoiturages', [TripController::class, 'index'])->name('trips.index');
Route::get('/covoiturages/confirmer/{id}', [TripController::class, 'confirm'])->name('trips.confirm');
Route::get('/covoiturages/participer/{id}', [TripController::class, 'participate'])->name('trips.participate');
Route::get('/covoiturages/{id}', [TripController::class, 'show'])->name('trips.show');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/mentions-legales', function () {
    return view('mentions-legales');
})->name('mentions-legales');

Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

Route::middleware('auth')->group(function () {
    Route::get('/profil', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profil', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profil', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('/tableau-de-bord/utilisateurs', [DashboardController::class, 'users'])->name('dashboard_users');
    Route::get('/tableau-de-bord/admin', [DashboardController::class, 'admin'])->name('dashboard_admin')->middleware('role:admin');
    Route::get('/tableau-de-bord/employe', [DashboardController::class, 'employe'])->name('dashboard_employe')->middleware('role:admin,employe');

    Route::post('/role/update', [DashboardController::class, 'updateRole'])->name('role.update');
    Route::post('/profile/photo/update', [DashboardController::class, 'updateProfilePhoto'])->name('profile.photo.update');
    Route::delete('/profile/photo/delete', [DashboardController::class, 'deleteProfilePhoto'])->name('profile.photo.delete');

    Route::post('/vehicles', [VehicleController::class, 'store'])->name('vehicle.store');
    Route::put('/vehicles/{id}', [VehicleController::class, 'update'])->name('vehicle.update');
    Route::delete('/vehicles/{id}', [VehicleController::class, 'destroy'])->name('vehicle.destroy');
    Route::get('/vehicles/{id}', [VehicleController::class, 'show'])->name('vehicle.show');
    Route::get('/vehicles/{id}/check-trips', [VehicleController::class, 'checkTrips'])->name('vehicle.check.trips');

    Route::post('/trips', [TripController::class, 'store'])->name('trip.store');
    Route::delete('/trips/{id}/cancel', [TripController::class, 'cancel'])->name('trip.cancel');
    Route::post('/trips/{id}/start', [TripController::class, 'start'])->name('trip.start');
    Route::post('/trips/{id}/end', [TripController::class, 'end'])->name('trip.end');
    Route::post('/trips/{id}/participate', [TripController::class, 'participate'])->name('trip.participate');

    // Routes d'administration
    Route::delete('/admin/users/{id}', [DashboardController::class, 'deleteUser'])->name('admin.user.delete');
    Route::patch('/admin/users/{id}/role', [DashboardController::class, 'changeUserRole'])->name('admin.user.role');
    Route::delete('/admin/trips/{id}', [DashboardController::class, 'deleteTrip'])->name('admin.trip.delete');
    Route::delete('/admin/vehicles/{id}', [DashboardController::class, 'deleteVehicle'])->name('admin.vehicle.delete');
});

require __DIR__ . '/auth.php';
