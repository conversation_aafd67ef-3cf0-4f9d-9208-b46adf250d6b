<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\TripController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Route de recherche qui redirige vers /covoiturages avec les résultats
Route::post('/rechercher', [TripController::class, 'search'])->name('search.covoiturage');

// Route temporaire pour tester les cards avec données simulées
Route::get('/test-cards', function () {
    // Créer des données simulées pour tester les cards
    $covoiturages = collect([
        (object) [
            'id' => 1,
            'pseudo_chauffeur' => '<PERSON>',
            'photo_chauffeur_data' => null,
            'note_chauffeur' => 4.8,
            'places_restantes' => 2,
            'prix' => 25,
            'ecologique' => true,
            'city_dep' => 'Paris',
            'city_arr' => 'Lyon',
            'departure_date' => '2024-12-25',
            'departure_time' => '08:30:00',
            'arrival_time' => '11:30:00',
        ],
        (object) [
            'id' => 2,
            'pseudo_chauffeur' => '<PERSON>',
            'photo_chauffeur_data' => null,
            'note_chauffeur' => 'Nouveau conducteur',
            'places_restantes' => 1,
            'prix' => 18,
            'ecologique' => false,
            'city_dep' => 'Marseille',
            'city_arr' => 'Nice',
            'departure_date' => '2024-12-26',
            'departure_time' => '14:15:00',
            'arrival_time' => '16:15:00',
        ],
        (object) [
            'id' => 3,
            'pseudo_chauffeur' => 'Sophie Bernard',
            'photo_chauffeur_data' => null,
            'note_chauffeur' => 5.0,
            'places_restantes' => 3,
            'prix' => 35,
            'ecologique' => true,
            'city_dep' => 'Toulouse',
            'city_arr' => 'Bordeaux',
            'departure_date' => '2024-12-27',
            'departure_time' => '10:00:00',
            'arrival_time' => '14:00:00',
        ],
        (object) [
            'id' => 4,
            'pseudo_chauffeur' => 'Pierre Durand',
            'photo_chauffeur_data' => null,
            'note_chauffeur' => 4.2,
            'places_restantes' => 4,
            'prix' => 12,
            'ecologique' => false,
            'city_dep' => 'Lille',
            'city_arr' => 'Bruxelles',
            'departure_date' => '2024-12-28',
            'departure_time' => '16:45:00',
            'arrival_time' => '18:30:00',
        ],
        (object) [
            'id' => 5,
            'pseudo_chauffeur' => 'Camille Moreau',
            'photo_chauffeur_data' => null,
            'note_chauffeur' => 'Nouveau conducteur',
            'places_restantes' => 2,
            'prix' => 22,
            'ecologique' => true,
            'city_dep' => 'Nantes',
            'city_arr' => 'Rennes',
            'departure_date' => '2024-12-29',
            'departure_time' => '09:15:00',
            'arrival_time' => '11:00:00',
        ],
    ]);

    return view('trips.index', compact('covoiturages'));
})->name('test-cards');

// Routes covoiturage en français
Route::get('/covoiturages', [TripController::class, 'index'])->name('trips.index');
Route::get('/covoiturages/confirmer/{id}', [TripController::class, 'confirm'])->name('trips.confirm');
Route::get('/covoiturages/participer/{id}', [TripController::class, 'participate'])->name('trips.participate');
Route::get('/covoiturages/{id}', [TripController::class, 'show'])->name('trips.show');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/mentions-legales', function () {
    return view('mentions-legales');
})->name('mentions-legales');

Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

Route::middleware('auth')->group(function () {
    // Routes de profil en français
    Route::get('/profil', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profil', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profil', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Routes de tableau de bord en français
    Route::get('/tableau-de-bord/utilisateurs', function () {
        return view('dashboard.users');
    })->name('dashboard_users');

    Route::get('/tableau-de-bord/admin', function () {
        return view('dashboard.admin');
    })->name('dashboard_admin');

    Route::get('/tableau-de-bord/employe', function () {
        return view('dashboard.employe');
    })->name('dashboard_employe');
});

require __DIR__ . '/auth.php';
