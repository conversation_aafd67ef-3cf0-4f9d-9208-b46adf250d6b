<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Utilisateur - EcoRide</title>

    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="{{ asset('css/partials/_dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    @include('partials.header')

    <div class="dashboard-container">
        <h1 class="dashboard-title">Mon Tableau de Bord</h1>

        @if (session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <div class="dashboard-grid">
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-user"></i> Mon Profil</h2>
                </div>
                <div class="widget-content">
                    <div class="profile-section">
                        <div class="profile-avatar">
                            @if ($profile_photo)
                                <img src="data:{{ $profile_photo_mime }};base64,{{ $profile_photo }}"
                                    alt="Photo de profil">
                            @else
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            @endif
                        </div>
                        <div class="profile-details">
                            <h3>{{ Auth::user()->name }}</h3>
                            <p><i class="fas fa-envelope"></i> {{ Auth::user()->email }}</p>
                            <p><i class="fas fa-calendar"></i> Membre depuis
                                {{ Auth::user()->created_at->format('d/m/Y') }}</p>
                            <div class="profile-credits">
                                <span class="credits-amount">{{ Auth::user()->credits ?? 0 }}</span>
                                <span class="credits-label">crédits</span>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('profile.photo.update') }}" method="POST" enctype="multipart/form-data"
                        style="margin-top: 1rem;">
                        @csrf
                        <input type="file" name="profile_photo" accept="image/*" class="form-control">
                        <button type="submit" class="btn-base btn-primary" style="margin-top: 0.5rem;">Mettre à jour la
                            photo</button>
                    </form>

                    @if ($profile_photo)
                        <form action="{{ route('profile.photo.delete') }}" method="POST" style="margin-top: 0.5rem;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-base btn-danger">Supprimer la photo</button>
                        </form>
                    @endif
                </div>
            </div>

            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-user-tag"></i> Mon Rôle</h2>
                </div>
                <div class="widget-content">
                    <div class="current-role">
                        <span class="role-label">Rôle actuel :</span>
                        <span class="role-value {{ Auth::user()->role }}">
                            @if (Auth::user()->role === 'passager')
                                Passager
                            @elseif(Auth::user()->role === 'conducteur')
                                Conducteur
                            @elseif(Auth::user()->role === 'les_deux')
                                Les deux
                            @else
                                {{ Auth::user()->role }}
                            @endif
                        </span>
                    </div>

                    <form action="{{ route('role.update') }}" method="POST">
                        @csrf
                        <div class="role-options">
                            <label class="role-option {{ Auth::user()->role === 'passager' ? 'selected' : '' }}">
                                <div class="role-radio">
                                    <input type="radio" name="role" value="passager"
                                        {{ Auth::user()->role === 'passager' ? 'checked' : '' }}>
                                    <div>
                                        <div class="role-name">Passager</div>
                                        <div class="role-desc">Je souhaite uniquement voyager en tant que passager</div>
                                    </div>
                                </div>
                            </label>

                            <label class="role-option {{ Auth::user()->role === 'conducteur' ? 'selected' : '' }}">
                                <div class="role-radio">
                                    <input type="radio" name="role" value="conducteur"
                                        {{ Auth::user()->role === 'conducteur' ? 'checked' : '' }}>
                                    <div>
                                        <div class="role-name">Conducteur</div>
                                        <div class="role-desc">Je souhaite uniquement proposer des trajets</div>
                                    </div>
                                </div>
                            </label>

                            <label class="role-option {{ Auth::user()->role === 'les_deux' ? 'selected' : '' }}">
                                <div class="role-radio">
                                    <input type="radio" name="role" value="les_deux"
                                        {{ Auth::user()->role === 'les_deux' ? 'checked' : '' }}>
                                    <div>
                                        <div class="role-name">Les deux</div>
                                        <div class="role-desc">Je souhaite à la fois proposer et participer à des
                                            trajets</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <button type="submit" class="role-submit-btn">Mettre à jour mon rôle</button>
                    </form>
                </div>
            </div>

            @if (in_array(Auth::user()->role, ['conducteur', 'les_deux']))
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h2><i class="fas fa-car"></i> Mes Véhicules</h2>
                        <button class="btn-base btn-success" onclick="openVehicleModal()">Ajouter un véhicule</button>
                    </div>
                    <div class="widget-content">
                        @if ($vehicles->count() > 0)
                            <div class="vehicles-list">
                                @foreach ($vehicles as $vehicle)
                                    <div class="vehicle-item">
                                        <div class="vehicle-info">
                                            <h4>{{ $vehicle->marque }} {{ $vehicle->modele }}</h4>
                                            <p>{{ $vehicle->immatriculation }} - {{ $vehicle->couleur }}</p>
                                            <p>{{ $vehicle->nb_places }} places - {{ $vehicle->energie }} -
                                                {{ $vehicle->annee }}</p>
                                        </div>
                                        <div class="vehicle-actions">
                                            <button class="btn-base btn-warning"
                                                onclick="editVehicle({{ $vehicle->id }})">Modifier</button>
                                            <button class="btn-base btn-danger"
                                                onclick="deleteVehicle({{ $vehicle->id }})">Supprimer</button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p>Aucun véhicule enregistré. Ajoutez votre premier véhicule pour commencer à proposer des
                                trajets.</p>
                        @endif
                    </div>
                </div>

                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h2><i class="fas fa-plus-circle"></i> Créer un Covoiturage</h2>
                    </div>
                    <div class="widget-content">
                        @if ($vehicles->count() > 0)
                            <button class="btn-base btn-success" onclick="openTripModal()">Créer un nouveau
                                trajet</button>
                        @else
                            <p>Vous devez d'abord ajouter un véhicule pour créer un covoiturage.</p>
                        @endif
                    </div>
                </div>

                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h2><i class="fas fa-route"></i> Mes Trajets Proposés</h2>
                    </div>
                    <div class="widget-content">
                        @if ($offeredTrips->count() > 0)
                            <div class="trips-list">
                                @foreach ($offeredTrips as $trip)
                                    <div class="trip-item">
                                        <div class="trip-info">
                                            <h4>{{ $trip->lieu_depart }} → {{ $trip->lieu_arrivee }}</h4>
                                            <p>{{ $trip->date_depart_formatted }} à
                                                {{ $trip->heure_depart_formatted }}</p>
                                            <p>{{ $trip->places_disponibles }}/{{ $trip->places_totales }} places -
                                                {{ $trip->prix }}€</p>
                                            <span
                                                class="status-badge status-{{ $trip->statut }}">{{ $trip->statut_formate }}</span>
                                        </div>
                                        <div class="trip-actions">
                                            @if ($trip->statut === 'planifie')
                                                <button class="btn-base btn-primary"
                                                    onclick="startTrip({{ $trip->id }})">Démarrer</button>
                                                <button class="btn-base btn-danger"
                                                    onclick="cancelTrip({{ $trip->id }})">Annuler</button>
                                            @elseif($trip->statut === 'en_cours')
                                                <button class="btn-base btn-success"
                                                    onclick="endTrip({{ $trip->id }})">Terminer</button>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p>Aucun trajet proposé pour le moment.</p>
                        @endif
                    </div>
                </div>
            @endif

            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-ticket-alt"></i> Mes Réservations</h2>
                </div>
                <div class="widget-content">
                    @if ($reservations->count() > 0)
                        <div class="reservations-list">
                            @foreach ($reservations as $reservation)
                                <div class="reservation-item">
                                    <div class="reservation-info">
                                        <h4>{{ $reservation->lieu_depart }} → {{ $reservation->lieu_arrivee }}</h4>
                                        <p>{{ $reservation->date_depart }} à {{ $reservation->heure_depart }}</p>
                                        <p>Conducteur: {{ $reservation->conducteur->name ?? 'N/A' }}</p>
                                        <p>Prix: {{ $reservation->prix }}€</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p>Aucune réservation en cours.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @include('partials.footer')

    <div id="vehicleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Ajouter un véhicule</h3>
                <button class="close" onclick="closeVehicleModal()">&times;</button>
            </div>
            <form id="vehicleForm" action="{{ route('vehicle.store') }}" method="POST">
                @csrf
                <div class="form-group">
                    <label class="form-label">Marque</label>
                    <input type="text" name="marque" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Modèle</label>
                    <input type="text" name="modele" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Immatriculation</label>
                    <input type="text" name="immatriculation" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Couleur</label>
                    <input type="text" name="couleur" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Nombre de places</label>
                    <select name="nb_places" class="form-select" required>
                        <option value="">Sélectionner</option>
                        @for ($i = 2; $i <= 9; $i++)
                            <option value="{{ $i }}">{{ $i }} places</option>
                        @endfor
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Énergie</label>
                    <select name="energie" class="form-select" required>
                        <option value="">Sélectionner</option>
                        <option value="Essence">Essence</option>
                        <option value="Diesel">Diesel</option>
                        <option value="Electrique">Électrique</option>
                        <option value="Hybride">Hybride</option>
                        <option value="GPL">GPL</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Année</label>
                    <input type="number" name="annee" class="form-control" min="1990"
                        max="{{ date('Y') }}" required>
                </div>
                <button type="submit" class="btn-base btn-success">Ajouter le véhicule</button>
            </form>
        </div>
    </div>

    <script src="{{ asset('js/dashboard.js') }}"></script>
</body>

</html>
