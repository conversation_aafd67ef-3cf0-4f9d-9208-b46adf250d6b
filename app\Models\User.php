<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    protected $primaryKey = 'user_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'n_credit',
        'idphoto',
        'role',
        'pref_smoke',
        'pref_pet',
        'pref_libre',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'n_credit' => 'integer',
        ];
    }

    // Accesseurs pour compatibilité avec les dashboards
    public function getCreditsAttribute()
    {
        return $this->n_credit;
    }

    public function setCreditsAttribute($value)
    {
        $this->n_credit = $value;
    }

    public function getProfilePhotoAttribute()
    {
        return $this->idphoto;
    }

    public function setProfilePhotoAttribute($value)
    {
        $this->idphoto = $value;
    }

    // Relations
    public function voitures()
    {
        return $this->hasMany(Voiture::class, 'user_id', 'user_id');
    }

    public function covoiturages()
    {
        return $this->hasMany(Covoiturage::class, 'user_id', 'user_id');
    }
}
