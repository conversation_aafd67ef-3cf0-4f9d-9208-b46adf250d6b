<?php $__env->startSection('title', 'Covoiturage'); ?>

<?php $__env->startSection('content'); ?>
    <script src="<?php echo e(asset('js/date-restriction.js')); ?>"></script>
    <script src="<?php echo e(asset('js/suggestion-links.js')); ?>"></script>
    <script src="<?php echo e(asset('js/profile-photo-sync.js')); ?>"></script>
    <script src="<?php echo e(asset('js/trip-filters.js')); ?>"></script>
    <script src="<?php echo e(asset('js/trip-details-modal.js')); ?>"></script>
    <script src="<?php echo e(asset('js/rating-stars.js')); ?>"></script>
    <script src="<?php echo e(asset('js/participate-buttons.js')); ?>"></script>
    <div class="covoiturage-container">
        <h1 class="covoiturage-title">Rechercher un covoiturage</h1>

        <section class="search-section">
            <!-- Gestion des messages -->
            <?php if(isset($error)): ?>
                <div class="error-message">
                    <?php echo e($error); ?>

                </div>
            <?php endif; ?>

            <?php if(isset($success)): ?>
                <div class="alert-success">
                    <?php echo e($success); ?>

                </div>
            <?php endif; ?>

            <?php if(isset($info)): ?>
                <div class="info-message">
                    <?php echo e($info); ?>


                    <?php if(isset($suggested_date)): ?>
                        <form action="<?php echo e(route('search.covoiturage')); ?>" method="POST" class="suggested-date-form">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="lieu_depart" value="<?php echo e($lieu_depart); ?>">
                            <input type="hidden" name="lieu_arrivee" value="<?php echo e($lieu_arrivee); ?>">
                            <input type="hidden" name="date" value="<?php echo e($suggested_date); ?>">
                            Essayez plutôt le <strong><?php echo e(date('d/m/Y', strtotime($suggested_date))); ?></strong>
                            <button type="submit" class="suggested-date-btn">Rechercher à cette date</button>
                        </form>
                    <?php endif; ?>

                    <?php if(isset($suggestions)): ?>
                        <div class="date-suggestions">
                            <p>Nous n'avons pas de covoiturage à la date recherchée. Néanmoins, nous en avons
                                <?php $__currentLoopData = $suggestions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $suggestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($index == 0): ?>
                                        <?php if($suggestion['count'] > 1): ?>
                                            <?php echo e($suggestion['count']); ?> le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php else: ?>
                                            le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php endif; ?>
                                    <?php elseif($index == count($suggestions) - 1): ?>
                                        <?php if($suggestion['count'] > 1): ?>
                                            et <?php echo e($suggestion['count']); ?> le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php else: ?>
                                            et le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php if($suggestion['count'] > 1): ?>
                                            , <?php echo e($suggestion['count']); ?> le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php else: ?>
                                            , le <a href="#" class="suggestion-link"
                                                data-date="<?php echo e($suggestion['date']); ?>" data-depart="<?php echo e($lieu_depart); ?>"
                                                data-arrivee="<?php echo e($lieu_arrivee); ?>"><?php echo e($suggestion['formatted_date']); ?></a>
                                            (<?php echo e($suggestion['diff']); ?>)
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                ... Pourquoi ne pas changer de date si vous êtes flexible?
                            </p>

                            <!-- Formulaire caché pour les suggestions -->
                            <form id="suggestion-form" action="<?php echo e(route('search.covoiturage')); ?>" method="POST"
                                style="display: none;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" id="suggestion-lieu-depart" name="lieu_depart" value="">
                                <input type="hidden" id="suggestion-lieu-arrivee" name="lieu_arrivee" value="">
                                <input type="hidden" id="suggestion-date" name="date" value="">
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Formulaire de recherche -->
            <form class="search-form" action="<?php echo e(route('search.covoiturage')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="form-group">
                    <label for="lieu_depart">Départ</label>
                    <input type="text" id="lieu_depart" name="lieu_depart" placeholder="Ville de départ" required
                        value="<?php echo e(old('lieu_depart') ?? ($lieu_depart ?? request('lieu_depart'))); ?>">
                </div>
                <div class="form-group">
                    <label for="lieu_arrivee">Arrivée</label>
                    <input type="text" id="lieu_arrivee" name="lieu_arrivee" placeholder="Ville d'arrivée" required
                        value="<?php echo e(old('lieu_arrivee') ?? ($lieu_arrivee ?? request('lieu_arrivee'))); ?>">
                </div>
                <div class="form-group">
                    <label for="date">Date</label>
                    <input type="date" id="date" name="date" required
                        value="<?php echo e(old('date') ?? ($date_recherche ?? request('date'))); ?>">
                </div>
                <button type="submit" class="search-button">Rechercher un trajet</button>
            </form>
        </section>

        <!-- Section des filtres -->
        <?php echo $__env->make('trips.partials.filters', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php if(isset($covoiturages) && count($covoiturages) > 0): ?>
            <div class="results-title">
                <h2>Trajets disponibles</h2>
                <p><?php echo e(count($covoiturages)); ?> résultat(s) trouvé(s)</p>
            </div>

            <section class="covoiturage-list">
                <?php $__currentLoopData = $covoiturages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $covoiturage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="covoiturage-card" data-max-travel-time="<?php echo e($covoiturage->max_travel_time ?? 120); ?>">
                        <div class="covoiturage-top-info">
                            <div class="covoiturage-driver">
                                <?php if($covoiturage->has_photo && $covoiturage->photo_chauffeur_data): ?>
                                    <img src="<?php echo e($covoiturage->photo_chauffeur_data); ?>"
                                        alt="<?php echo e($covoiturage->pseudo_chauffeur); ?>" class="driver-photo">
                                <?php else: ?>
                                    <div class="driver-photo photo-placeholder">
                                        <i class="fas fa-user svg-inline--fa"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="driver-info">
                                    <h3><?php echo e($covoiturage->pseudo_chauffeur); ?></h3>
                                    <div class="driver-rating">
                                        <span class="rating-value"><?php echo e($covoiturage->note_chauffeur); ?></span>
                                        <span class="rating-stars">
                                            <!-- C'est le JS qui génére les étoiles -->
                                            <?php if(!is_numeric($covoiturage->note_chauffeur)): ?>
                                                <span>Nouveau conducteur</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="covoiturage-booking-info">
                                <div class="trip-seats">
                                    <i class="fas fa-user"></i>
                                    <span><?php echo e($covoiturage->places_restantes); ?>

                                        <?php echo e($covoiturage->places_restantes > 1 ? 'places disponibles' : 'place disponible'); ?></span>
                                </div>
                                <div class="trip-price">
                                    <span class="price-value"><?php echo e($covoiturage->prix); ?> crédits</span>
                                    <span class="price-per-person">par personne</span>
                                </div>
                            </div>
                        </div>

                        <div class="covoiturage-driver">
                            <?php if($covoiturage->has_photo && $covoiturage->photo_chauffeur_data): ?>
                                <img src="<?php echo e($covoiturage->photo_chauffeur_data); ?>"
                                    alt="<?php echo e($covoiturage->pseudo_chauffeur); ?>" class="driver-photo">
                            <?php else: ?>
                                <div class="driver-photo photo-placeholder">
                                    <i class="fas fa-user svg-inline--fa"></i>
                                </div>
                            <?php endif; ?>
                            <div class="driver-info">
                                <h3><?php echo e($covoiturage->pseudo_chauffeur); ?></h3>
                                <div class="driver-rating">
                                    <span class="rating-value"><?php echo e($covoiturage->note_chauffeur); ?></span>
                                    <span class="rating-stars">
                                        <!-- C'est le JS qui génére les étoiles -->
                                        <?php if(!is_numeric($covoiturage->note_chauffeur)): ?>
                                            <span>Nouveau conducteur</span>
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                        </div>


                        <div class="covoiturage-details">
                            <div class="trip-info-container">
                                <div class="trip-info-left">
                                    <div class="trip-route">
                                        <span class="from"><?php echo e($covoiturage->lieu_depart); ?></span>
                                        <span class="route-arrow">→</span>
                                        <span class="to"><?php echo e($covoiturage->lieu_arrivee); ?></span>
                                    </div>
                                    <div class="trip-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo e(date('d/m/Y', strtotime($covoiturage->date_depart))); ?>

                                    </div>
                                </div>
                                <div class="trip-time">
                                    <span class="departure-time">
                                        <i class="fas fa-clock"></i>
                                        Départ: <?php echo e(substr($covoiturage->heure_depart, 0, 5)); ?>

                                    </span>
                                    <span class="arrival-time">
                                        <i class="fas fa-clock"></i>
                                        Arrivée: <?php echo e(substr($covoiturage->heure_arrivee, 0, 5)); ?>

                                    </span>
                                </div>
                            </div>

                            <div class="trip-eco-badge <?php echo e($covoiturage->ecologique ? 'eco' : 'standard'); ?>">
                                <?php if($covoiturage->ecologique): ?>
                                    <i class="fas fa-leaf"></i> Voyage écologique
                                <?php else: ?>
                                    <i class="fas fa-car"></i> Voyage standard
                                <?php endif; ?>
                            </div>
                        </div>


                        <div class="covoiturage-booking">
                            <div class="trip-seats">
                                <i class="fas fa-user"></i>
                                <span><?php echo e($covoiturage->places_restantes); ?>

                                    <?php echo e($covoiturage->places_restantes > 1 ? 'places disponibles' : 'place disponible'); ?></span>
                            </div>

                            <div class="trip-price">
                                <span class="price-value"><?php echo e($covoiturage->prix); ?> crédits</span>
                                <span class="price-per-person">par personne</span>
                            </div>

                            <div class="booking-buttons">
                                <a href="<?php echo e(route('trips.confirm', ['id' => $covoiturage->id ?? 1])); ?>"
                                    class="btn-base btn-details" data-id="<?php echo e($covoiturage->id ?? 1); ?>">
                                    Détails
                                </a>
                                <a href="<?php echo e(route('trips.participate', ['id' => $covoiturage->id ?? 1])); ?>"
                                    class="btn-base btn-participate">
                                    Participer
                                </a>
                            </div>
                        </div>

                        <div class="mobile-buttons">
                            <a href="<?php echo e(route('trips.confirm', ['id' => $covoiturage->id ?? 1])); ?>"
                                class="btn-base btn-details" data-id="<?php echo e($covoiturage->id ?? 1); ?>">
                                Détails
                            </a>
                            <a href="<?php echo e(route('trips.participate', ['id' => $covoiturage->id ?? 1])); ?>"
                                class="btn-base btn-participate">
                                Participer
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </section>
            <?php elseif(request()->isMethod('POST') || isset($_GET['error'])): ?>
                <div class="no-results">
                    <p>Aucun covoiturage disponible correspondant à vos critères.</p>
                    <p>Essayez de modifier votre recherche ou consultez nos suggestions ci-dessus.</p>
                </div>
            <?php else: ?>
                <div class="welcome-message">
                    <div class="welcome-icon">🚗</div>
                    <h2>Bienvenue sur la page de covoiturage</h2>
                    <p>Utilisez le formulaire ci-dessus pour trouver votre prochain trajet écologique et économique.</p>
                    <div class="welcome-tips">
                        <h3>Conseils pour votre recherche :</h3>
                        <ul>
                            <li>Soyez précis sur les noms de villes</li>
                            <li>Essayez différentes dates pour plus d'options</li>
                            <li>Les voyages écologiques sont indiqués par un badge vert</li>
                        </ul>
                    </div>
                </div>

            <?php endif; ?>

        </div>

        <!-- Inclure la modale => détails des covoit -->
        <?php echo $__env->make('trips.trip-details-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/views/trips/index.blade.php ENDPATH**/ ?>