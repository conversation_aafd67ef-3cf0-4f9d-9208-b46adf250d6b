<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rechercher un Covoiturage - EcoRide</title>
    
    <link rel="stylesheet" href="<?php echo e(asset('css/app.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
</head>
<body>
    <?php echo $__env->make('partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="dashboard-container">
        <h1 class="dashboard-title">Rechercher un Covoiturage</h1>

        <?php if(session('success')): ?>
            <div class="alert alert-success"><?php echo e(session('success')); ?></div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
        <?php endif; ?>

        <!-- Formulaire de recherche -->
        <div class="dashboard-widget">
            <div class="widget-header">
                <h2><i class="fas fa-search"></i> Rechercher un trajet</h2>
            </div>
            <div class="widget-content">
                <form action="<?php echo e(route('search.covoiturage')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div class="form-group">
                            <label class="form-label">Lieu de départ</label>
                            <input type="text" name="lieu_depart" class="form-control" 
                                   value="<?php echo e(old('lieu_depart')); ?>" 
                                   placeholder="Ex: Paris, Lyon..." required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Lieu d'arrivée</label>
                            <input type="text" name="lieu_arrivee" class="form-control" 
                                   value="<?php echo e(old('lieu_arrivee')); ?>" 
                                   placeholder="Ex: Marseille, Nice..." required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Date de départ</label>
                            <input type="date" name="date_depart" class="form-control" 
                                   value="<?php echo e(old('date_depart', date('Y-m-d'))); ?>" 
                                   min="<?php echo e(date('Y-m-d')); ?>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Nombre de passagers</label>
                            <select name="nb_passagers" class="form-select" required>
                                <option value="">Sélectionner</option>
                                <?php for($i = 1; $i <= 8; $i++): ?>
                                    <option value="<?php echo e($i); ?>" <?php echo e(old('nb_passagers') == $i ? 'selected' : ''); ?>>
                                        <?php echo e($i); ?> passager<?php echo e($i > 1 ? 's' : ''); ?>

                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn-base btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </form>
            </div>
        </div>

        <!-- Résultats de recherche -->
        <?php if($covoiturages->count() > 0): ?>
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-list"></i> Résultats de recherche (<?php echo e($covoiturages->count()); ?> trajet<?php echo e($covoiturages->count() > 1 ? 's' : ''); ?> trouvé<?php echo e($covoiturages->count() > 1 ? 's' : ''); ?>)</h2>
                </div>
                <div class="widget-content">
                    <div class="trips-results">
                        <?php $__currentLoopData = $covoiturages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $covoiturage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="trip-result-item">
                                <div class="trip-main-info">
                                    <div class="trip-route">
                                        <h3><?php echo e($covoiturage->lieu_depart); ?> <i class="fas fa-arrow-right"></i> <?php echo e($covoiturage->lieu_arrivee); ?></h3>
                                        <p class="trip-datetime">
                                            <i class="fas fa-calendar"></i> <?php echo e($covoiturage->date_depart_formatted); ?>

                                            <i class="fas fa-clock"></i> <?php echo e($covoiturage->heure_depart_formatted); ?>

                                        </p>
                                    </div>
                                    <div class="trip-details">
                                        <div class="trip-driver">
                                            <i class="fas fa-user"></i>
                                            <span><?php echo e($covoiturage->conducteur->name); ?></span>
                                        </div>
                                        <div class="trip-vehicle">
                                            <i class="fas fa-car"></i>
                                            <span><?php echo e($covoiturage->voiture->brand); ?> <?php echo e($covoiturage->voiture->model); ?></span>
                                            <?php if($covoiturage->voiture->is_ecologique): ?>
                                                <span class="eco-badge"><i class="fas fa-leaf"></i> Écologique</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="trip-places">
                                            <i class="fas fa-users"></i>
                                            <span><?php echo e($covoiturage->places_disponibles); ?> place<?php echo e($covoiturage->places_disponibles > 1 ? 's' : ''); ?> disponible<?php echo e($covoiturage->places_disponibles > 1 ? 's' : ''); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="trip-price-action">
                                    <div class="trip-price">
                                        <span class="price-amount"><?php echo e($covoiturage->prix); ?>€</span>
                                        <span class="price-label">par personne</span>
                                    </div>
                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if($covoiturage->user_id !== Auth::user()->user_id): ?>
                                            <a href="<?php echo e(route('trips.confirm', $covoiturage->covoit_id)); ?>" class="btn-base btn-success">
                                                <i class="fas fa-check"></i> Réserver
                                            </a>
                                        <?php else: ?>
                                            <span class="btn-base btn-secondary">Votre trajet</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('login')); ?>" class="btn-base btn-primary">
                                            <i class="fas fa-sign-in-alt"></i> Se connecter pour réserver
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php elseif(request()->isMethod('post')): ?>
            <div class="dashboard-widget">
                <div class="widget-content">
                    <div class="no-results">
                        <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                        <h3>Aucun covoiturage trouvé</h3>
                        <p>Aucun trajet ne correspond à vos critères de recherche.</p>
                        <p>Essayez de modifier vos critères ou créez votre propre trajet !</p>
                        <?php if(auth()->guard()->check()): ?>
                            <?php if(in_array(Auth::user()->role, ['Conducteur', 'Les deux'])): ?>
                                <a href="<?php echo e(route('dashboard_users')); ?>" class="btn-base btn-primary">
                                    <i class="fas fa-plus"></i> Créer un trajet
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <style>
        .trips-results {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .trip-result-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            background: white;
            transition: box-shadow 0.3s ease;
        }

        .trip-result-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .trip-main-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .trip-route h3 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 1.3rem;
        }

        .trip-route .fa-arrow-right {
            color: #3498db;
            margin: 0 0.5rem;
        }

        .trip-datetime {
            color: #7f8c8d;
            margin: 0;
        }

        .trip-datetime i {
            margin-right: 0.5rem;
        }

        .trip-details {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            text-align: right;
        }

        .trip-details > div {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        .trip-details i {
            color: #3498db;
            width: 16px;
        }

        .eco-badge {
            background: #2ecc71;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }

        .trip-price-action {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        .trip-price {
            text-align: left;
        }

        .price-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2ecc71;
        }

        .price-label {
            display: block;
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .no-results {
            text-align: center;
            padding: 3rem 1rem;
        }

        .no-results h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .no-results p {
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .trip-main-info {
                flex-direction: column;
                gap: 1rem;
            }

            .trip-details {
                text-align: left;
            }

            .trip-details > div {
                justify-content: flex-start;
            }

            .trip-price-action {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .trip-price {
                text-align: center;
            }
        }
    </style>
</body>
</html>
<?php /**PATH /var/www/html/resources/views/trips/index.blade.php ENDPATH**/ ?>