/* Styles pour la section filtre */
.filters-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filters-title {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #333;
}

.filters-container {
    display: flex;
    gap: 20px;
}

.filter-group {
    flex: 1 1 300px;
    margin-bottom: 15px;
}

.filter-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    text-align: center;
}

.filter-label-checkbox {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    gap: 1.2rem;
}

.filter-checkbox {
    margin-right: 8px;
}

.filter-text {
    vertical-align: middle;
}

.filter-range {
    width: 100%;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: #ddd;
    outline: none;
    border-radius: 4px;
    margin-bottom: 10px;
}

.filter-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #2ecc71;
    border-radius: 50%;
    cursor: pointer;
}

.filter-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #2ecc71;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}


#duration-value {
    display: flex;
    justify-content: center;
    color: inherit;
    font-weight: inherit;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #666;
}

.rating-filter {
    display: flex;
    gap: 5px;
}

.filter-rating-flex {
    display: flex;
    flex-direction: column;
    align-items: center;
}


.rating-filter .star {
    font-size: 1.5rem;
    color: #2c3e50;
    cursor: pointer;
    transition: color 0.2s;
}

.rating-filter .star.active {
    color: #f39c12;
}

/* Responsive design */
@media (max-width: 1072px) {
    .filters-container {
        flex-wrap: wrap;
    }

    .filter-group {
        flex: 1 1 calc(50% - 20px);
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .filters-container {
        flex-direction: column;
    }

    .filter-group {
        flex: 1 1 100%;
    }
}

/* Styles spécifiques pour les très petits écrans (0-360px) */
@media (max-width: 360px) {
    .filters-section {
        padding: 15px 10px;
        overflow: hidden;
    }

    .filters-container {
        width: 100%;
        overflow-x: hidden;
    }

    .filter-group {
        width: 100%;
        min-width: 0;
        max-width: 100%;
        box-sizing: border-box;
        padding-right: 5px;
    }

    .filter-label {
        font-size: 0.9rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .filter-text {
        font-size: 0.9rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .filter-range {
        max-width: 100%;
    }

    .range-labels {
        font-size: 0.7rem;
    }

    .rating-filter {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 3px;
    }

    .rating-filter .star {
        font-size: 1.2rem;
    }

    /* Ajustement pour le filtre écologique */
    .filter-label-checkbox {
        gap: 0.8rem;
    }

    /* Ajustement pour les valeurs affichées */
    #price-value, #duration-value {
        font-size: 0.9rem;
    }

    /* Gestion des messages d'avertissement pour les prix/durées identiques */
    #price-value[style*="color: red"], #duration-value[style*="color: red"] {
        font-size: 0.8rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        display: inline-block;
        max-width: 100%;
        line-height: 1.2;
    }
}

/* Animation pour les éléments filtrés */

.covoiturage-card.filtered-out {
    opacity: 0;
    transform: scale(0.95);
    height: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    pointer-events: none;
}
