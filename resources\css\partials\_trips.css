/* Pages covoiturage = trips.blade.php */
.covoiturage-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 40px 60px 40px;
}

.covoiturage-title {
    color: #2c3e50;
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.covoiturage-container .search-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 3rem;
}

.covoiturage-container .search-section h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Titre */
.results-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.results-title h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin: 0;
}

.results-title p {
    color: #7f8c8d;
    font-size: 1rem;
}

/* Liste des covoiturages */
.covoiturage-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Card de base */
.covoiturage-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
}

.covoiturage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* Pour mobile uniquement */
.covoiturage-top-info {
    display: flex;
    justify-content: space-between;
    background-color: #f9f9f9;
    border-bottom: 1px solid #f0f0f0;
    padding: 1rem;
}

.covoiturage-top-info .covoiturage-driver {
    width: 50%;
    display: flex;
    padding: 0;
    background-color: transparent;
    border: none;
}

.covoiturage-booking-info {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
}

.covoiturage-booking-info .trip-seats {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.covoiturage-booking-info .price-value {
    font-size: 1.3rem;
}

.covoiturage-booking-info .price-per-person {
    font-size: 0.7rem;
}

/* Section chauffeur */
.covoiturage-driver {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f9f9f9;
}

.driver-photo {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #2ecc71;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    font-size: 2rem;
}

.driver-info h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
    text-align: center;
}


/* Notation (star)*/

.driver-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
    justify-content: center;
    flex-wrap: wrap;
}

.rating-value {
    font-weight: bold;
    color: #f39c12;
}


.rating-stars {
    color: #f39c12;
    letter-spacing: 2px;
}

.star {
    display: inline-block;
    font-size: 1.1rem;
    letter-spacing: 1px;
}

.star.filled {
    color: #f39c12;
}

.star.half-filled {
    position: relative;
    color: #f39c12;
    background: linear-gradient(90deg, #f39c12 50%, #dddddd 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.star.empty {
    color: #dddddd;
}





/* Section détails */
.covoiturage-details {
    padding: 1.5rem;
    flex: 1;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* info de trajet */
.trip-info-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.trip-info-left {
    display: flex;
    flex-direction: column;
}

.trip-route {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    font-weight: 500;
}

.route-arrow {
    color: #7f8c8d;
    font-size: 1.5rem;
}

.trip-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
}

.trip-date {
    font-size: 1.1rem;
    font-weight: 500;
    color: #2c3e50;
    text-align: left;
    margin-bottom: 0.5rem;
}

.fa-calendar, .fa-clock {
    margin-right: 0.4rem;
}




.trip-time {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    color: #7f8c8d;
}

.trip-eco-badge {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    align-self: center;
}

.trip-eco-badge.eco {
    background-color: rgba(46, 204, 113, 0.15);
    color: #27ae60;
}

.trip-eco-badge.standard {
    background-color: rgba(149, 165, 166, 0.15);
    color: #7f8c8d;
}

/* Section réservation */
.covoiturage-booking {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
}

.trip-seats {
    font-size: 1rem;
    color: #7f8c8d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trip-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 1rem 0;
}

.price-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2ecc71;
}

.price-per-person {
    font-size: 0.8rem;
    color: #95a5a6;
    margin-top: 0.25rem;
}

/* Boutons */
.btn-base {
    display: block;
    width: 100%;
    max-width: 150px;
    padding: 0.75rem 1.5rem;
    text-align: center;
    font-weight: 500;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-base:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Styles spécifiques pour chaque type de bouton */
.btn-participate {
    background-color: #2ecc71;
}

.btn-participate:hover {
    background-color: #27ae60;
}

.btn-details {
    background-color: #3498db;
}

.btn-details:hover {
    background-color: #2980b9;
}

.booking-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.mobile-buttons {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background-color: #f9f9f9;
    border-top: 1px solid #f0f0f0;
}

.mobile-buttons .btn-base {
    width: 48%;
    padding: 0.75rem 0;
    max-width: none;
}


/* Partie ORDI (grands écrans) ////////////////////////////////// */
/* Les cacher en mobile*/
@media (max-width: 820px) {
    .covoiturage-driver:not(.covoiturage-top-info .covoiturage-driver),
    .covoiturage-booking {
        display: none;
    }
}

/* Les cacher en ORDI */
@media (min-width: 820px) {
    .covoiturage-top-info,
    .mobile-buttons {
        display: none;
    }

    .covoiturage-card {
        flex-direction: row;
        align-items: stretch;
    }

    .covoiturage-driver {
        width: 25%;
        border-bottom: none;
        border-right: 1px solid #f0f0f0;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .driver-photo {
        width: 100px;
        height: 100px;
        font-size: 3rem;
    }

    /* Mise en page ORDI/desktop */
    .trip-info-container {
        display: block;
    }

    .trip-route {
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .trip-date {
        text-align: center;
    }

    .trip-time {
        flex-direction: row;
        justify-content: space-between;
    }

    .covoiturage-details {
        width: 45%;
        border-bottom: none;
        border-right: 1px solid #f0f0f0;
    }

    .covoiturage-booking {
        width: 30%;
        display: flex;
    }

    .booking-buttons {
        align-items: center;
    }
}

/* Adaptation pour les très petits écrans/////////////////// */
@media (max-width: 580px) {
    .covoiturage-title {
        font-size: 1.8rem;
    }

    .covoiturage-container .search-section {
        padding: 1.5rem;
    }

    .price-value {
        font-size: 1.5rem;
    }

    .welcome-message {
        padding: 2rem 1rem;
    }

    .trip-info-container {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .trip-info-left {
        align-items: center;
        width: 100%;
    }

    .trip-route {
        justify-content: center;
    }

    .trip-date, .trip-time {
        text-align: center;
        align-items: center;
        gap: 1rem;
    }

    .covoiturage-top-info {
        flex-direction: column;
    }

    .covoiturage-top-info .covoiturage-driver {
        width: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        gap: 0;
    }

    .covoiturage-top-info .driver-photo {
        margin: 0 auto 0.75rem;
    }

    .covoiturage-top-info .driver-info {
        text-align: center;
        width: 100%;
    }

    .covoiturage-booking-info {
        width: 100%;
        align-items: center;
    }

    .mobile-buttons {
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
    }

    .mobile-buttons .btn-base {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }

    .results-title {
        justify-content: center;
        flex-direction: column;
    }
}

@media (max-width: 300px) {
    .driver-rating {
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .trip-route {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .route-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }
}

/* Autre régle CSS */
.no-results {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
}

.no-results p {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 0.75rem;
}

.no-results p:last-child {
    font-size: 1rem;
    margin-bottom: 0;
}

.btn-return-home {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
    margin-top: 1rem;
}

.btn-return-home:hover {
    background-color: #2980b9;
}

.welcome-message {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    padding: 3rem 2rem;
    text-align: center;
    margin-top: 2rem;
}

.welcome-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.welcome-message h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.welcome-message p {
    color: #7f8c8d;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.welcome-tips {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1.5rem;
    max-width: 500px;
    margin: 0 auto;
    text-align: left;
}

.welcome-tips h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.welcome-tips ul {
    padding-left: 1.5rem;
}

.welcome-tips li {
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}