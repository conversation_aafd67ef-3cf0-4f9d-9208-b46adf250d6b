/* Page d'accueil */
.home {
    padding-top: 80px;
}

/* Hero section */
.hero {
    background: linear-gradient(135deg, #2ecc71, #3498db);
    color: white;
    padding: 2rem 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.hero h1 {
    font-size: 2rem;
}

.hero p {
    margin-bottom: 1rem;
}

/* Formulaire de recherche */
.search-section {
    padding: 5rem 1rem;
}

.search-section h1 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.search-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.search-button {
    padding: 1rem;
    width: 100%;
}

.search-button:focus {
    outline: 2px solid #27ae60;
    outline-offset: 2px;
}

/* Section présentation */
.presentation {
    padding: 4rem 1rem;
    background-color: white;
}

.text-content {
    max-width: 1200px;
    margin: 0 auto;
}

.text-content h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.text-content > p {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem auto;
}

.features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
}

.feature img {
    width: 100px;
    height: 100px;
    margin-bottom: 1rem;
}

.feature h3 {
    color: #2ecc71;
    margin-bottom: 0.5rem;
}

/* Date suggérée */
.suggested-date-form {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.suggested-date-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    margin-left: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.suggested-date-btn:hover {
    background-color: #2980b9;
}

/* Version ordi */
@media (min-width: 768px) {
    .search-form {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .form-group {
        margin-bottom: 0;
    }

    .search-button {
        grid-column: 1 / 4;
        margin-top: 1rem;
    }

    .features {
        grid-template-columns: repeat(3, 1fr);
    }
}
