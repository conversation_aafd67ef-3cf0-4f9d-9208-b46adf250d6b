<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Détails du Covoiturage - EcoRide</title>
    
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    @include('partials.header')

    <div class="dashboard-container">
        <div class="breadcrumb">
            <a href="{{ route('covoiturage') }}">Rechercher</a> > Détails du trajet
        </div>

        <h1 class="dashboard-title">Détails du Covoiturage</h1>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <div class="trip-details-container">
            <!-- Informations principales du trajet -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-route"></i> Informations du trajet</h2>
                </div>
                <div class="widget-content">
                    <div class="trip-route-info">
                        <div class="route-main">
                            <h3>{{ $covoiturage->lieu_depart }} <i class="fas fa-arrow-right"></i> {{ $covoiturage->lieu_arrivee }}</h3>
                        </div>
                        <div class="trip-details-grid">
                            <div class="detail-item">
                                <i class="fas fa-calendar"></i>
                                <span class="label">Date :</span>
                                <span class="value">{{ $covoiturage->date_depart_formatted }}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span class="label">Heure de départ :</span>
                                <span class="value">{{ $covoiturage->heure_depart_formatted }}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span class="label">Places disponibles :</span>
                                <span class="value">{{ $covoiturage->places_disponibles }} / {{ $covoiturage->places_totales }}</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-euro-sign"></i>
                                <span class="label">Prix par personne :</span>
                                <span class="value price">{{ $covoiturage->prix }}€</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-info-circle"></i>
                                <span class="label">Statut :</span>
                                <span class="value status status-{{ $covoiturage->statut }}">{{ $covoiturage->statut_formate }}</span>
                            </div>
                            @if($covoiturage->ecologique)
                                <div class="detail-item">
                                    <i class="fas fa-leaf"></i>
                                    <span class="label">Trajet écologique :</span>
                                    <span class="value eco-badge">Oui</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations du conducteur -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-user"></i> Conducteur</h2>
                </div>
                <div class="widget-content">
                    <div class="driver-info">
                        <div class="driver-avatar">
                            @if($covoiturage->conducteur->idphoto)
                                <img src="data:image/jpeg;base64,{{ base64_encode($covoiturage->conducteur->idphoto) }}" alt="Photo de profil">
                            @else
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            @endif
                        </div>
                        <div class="driver-details">
                            <h3>{{ $covoiturage->conducteur->name }}</h3>
                            <p class="driver-role">{{ $covoiturage->conducteur->role }}</p>
                            <div class="driver-preferences">
                                @if($covoiturage->conducteur->pref_smoke)
                                    <span class="pref-badge">
                                        <i class="fas fa-smoking"></i> {{ $covoiturage->conducteur->pref_smoke }}
                                    </span>
                                @endif
                                @if($covoiturage->conducteur->pref_pet)
                                    <span class="pref-badge">
                                        <i class="fas fa-paw"></i> Animaux {{ $covoiturage->conducteur->pref_pet }}
                                    </span>
                                @endif
                            </div>
                            @if($covoiturage->conducteur->pref_libre)
                                <div class="driver-notes">
                                    <h4>Notes du conducteur :</h4>
                                    <p>{{ $covoiturage->conducteur->pref_libre }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations du véhicule -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-car"></i> Véhicule</h2>
                </div>
                <div class="widget-content">
                    <div class="vehicle-info">
                        <div class="vehicle-details">
                            <h3>{{ $covoiturage->voiture->brand }} {{ $covoiturage->voiture->model }}</h3>
                            <div class="vehicle-specs">
                                <span class="spec-item">
                                    <i class="fas fa-palette"></i> {{ $covoiturage->voiture->color }}
                                </span>
                                <span class="spec-item">
                                    <i class="fas fa-users"></i> {{ $covoiturage->voiture->n_place }} places
                                </span>
                                <span class="spec-item">
                                    <i class="fas fa-gas-pump"></i> {{ $covoiturage->voiture->energie }}
                                </span>
                                @if($covoiturage->voiture->is_ecologique)
                                    <span class="spec-item eco">
                                        <i class="fas fa-leaf"></i> Écologique
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Passagers confirmés -->
            @if($confirmations->count() > 0)
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h2><i class="fas fa-users"></i> Passagers confirmés ({{ $confirmations->count() }})</h2>
                    </div>
                    <div class="widget-content">
                        <div class="passengers-list">
                            @foreach($confirmations as $confirmation)
                                <div class="passenger-item">
                                    <div class="passenger-info">
                                        <i class="fas fa-user-check"></i>
                                        <span class="passenger-name">{{ $confirmation->name }}</span>
                                        <span class="passenger-status status-{{ strtolower(str_replace(' ', '-', $confirmation->statut)) }}">
                                            {{ $confirmation->statut }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Actions -->
            <div class="trip-actions">
                @auth
                    @if($covoiturage->user_id !== Auth::user()->user_id && $covoiturage->places_disponibles > 0 && $covoiturage->statut === 'planifie')
                        <a href="{{ route('trips.confirm', $covoiturage->covoit_id) }}" class="btn-base btn-success btn-large">
                            <i class="fas fa-check"></i> Réserver ce trajet
                        </a>
                    @elseif($covoiturage->user_id === Auth::user()->user_id)
                        <div class="owner-actions">
                            <span class="info-text">C'est votre trajet</span>
                            @if($covoiturage->statut === 'planifie')
                                <button class="btn-base btn-warning" onclick="cancelTrip({{ $covoiturage->covoit_id }})">
                                    <i class="fas fa-times"></i> Annuler le trajet
                                </button>
                            @endif
                        </div>
                    @elseif($covoiturage->places_disponibles <= 0)
                        <span class="btn-base btn-secondary btn-large">
                            <i class="fas fa-users"></i> Complet
                        </span>
                    @endif
                @else
                    <a href="{{ route('login') }}" class="btn-base btn-primary btn-large">
                        <i class="fas fa-sign-in-alt"></i> Se connecter pour réserver
                    </a>
                @endauth
                
                <a href="{{ route('covoiturage') }}" class="btn-base btn-outline">
                    <i class="fas fa-arrow-left"></i> Retour à la recherche
                </a>
            </div>
        </div>
    </div>

    @include('partials.footer')

    <style>
        .breadcrumb {
            margin-bottom: 1rem;
            color: #7f8c8d;
        }

        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }

        .trip-details-container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .route-main h3 {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .route-main .fa-arrow-right {
            color: #3498db;
            margin: 0 1rem;
        }

        .trip-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-item i {
            color: #3498db;
            width: 20px;
        }

        .label {
            font-weight: 500;
            color: #2c3e50;
        }

        .value {
            margin-left: auto;
            font-weight: 600;
        }

        .price {
            color: #2ecc71;
            font-size: 1.1rem;
        }

        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .status-planifie { background: #3498db; color: white; }
        .status-en-cours { background: #f39c12; color: white; }
        .status-termine { background: #2ecc71; color: white; }
        .status-annule { background: #e74c3c; color: white; }

        .eco-badge {
            background: #2ecc71;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .driver-info {
            display: flex;
            gap: 1.5rem;
            align-items: flex-start;
        }

        .driver-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }

        .driver-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-placeholder {
            width: 100%;
            height: 100%;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #95a5a6;
            font-size: 2rem;
        }

        .driver-details h3 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
        }

        .driver-role {
            color: #7f8c8d;
            margin-bottom: 1rem;
        }

        .driver-preferences {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .pref-badge {
            background: #ecf0f1;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #2c3e50;
        }

        .driver-notes h4 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
        }

        .vehicle-specs {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .spec-item {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #2c3e50;
        }

        .spec-item.eco {
            background: #2ecc71;
            color: white;
        }

        .spec-item i {
            margin-right: 0.5rem;
        }

        .passengers-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .passenger-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .passenger-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .passenger-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .trip-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .owner-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .info-text {
            color: #7f8c8d;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .trip-details-grid {
                grid-template-columns: 1fr;
            }

            .driver-info {
                flex-direction: column;
                text-align: center;
            }

            .vehicle-specs {
                justify-content: center;
            }

            .trip-actions {
                flex-direction: column;
            }
        }
    </style>

    <script>
        function cancelTrip(tripId) {
            if (confirm('Êtes-vous sûr de vouloir annuler ce trajet ?')) {
                // Ici vous pouvez ajouter la logique d'annulation
                window.location.href = `/covoiturages/${tripId}/annuler`;
            }
        }
    </script>
</body>
</html>
