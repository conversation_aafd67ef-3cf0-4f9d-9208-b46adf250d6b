<?php $__env->startSection('title', 'Contactez-nous'); ?>

<?php $__env->startSection('content'); ?>
    <section class="login-section">
        <h2>Contactez-nous</h2>
        <p>* champs obligatoire</p>

        <!-- erreurs -->
        <?php if($errors->any()): ?>
            <div class="error-message">
                <ul>
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- succès -->
        <?php if(session('success')): ?>
            <div class="alert-success">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert-danger">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <form class="login-form" id="contact-form" method="POST" action="<?php echo e(route('contact.store')); ?>">
            <?php echo csrf_field(); ?>
            <div class="form-group">
                <label for="name">Nom *</label>
                <input type="text" id="name" name="name" required value="<?php echo e(old('name')); ?>">
            </div>
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" required value="<?php echo e(old('email')); ?>">
            </div>
            <div class="form-group">
                <label for="subject">Sujet *</label>
                <select id="subject" name="subject" required>
                    <option value="">Sélectionnez un sujet</option>
                    <option value="support" <?php echo e(old('subject') == 'support' ? 'selected' : ''); ?>>Support technique
                    </option>
                    <option value="reservation" <?php echo e(old('subject') == 'reservation' ? 'selected' : ''); ?>>Problème de
                        réservation</option>
                    <option value="other" <?php echo e(old('subject') == 'other' ? 'selected' : ''); ?>>Autre</option>
                </select>
            </div>
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" required rows="5" placeholder="Votre message..."><?php echo e(old('message')); ?></textarea>
            </div>
            <button type="submit" class="search-button">Envoyer le message</button>
        </form>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/views/contact.blade.php ENDPATH**/ ?>