<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Employé - EcoRide</title>

    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>

<body>
    @include('partials.header')

    <div class="dashboard-container">
        <h1 class="dashboard-title">Dashboard Employé</h1>

        @if (session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ $stats['pending_trips'] }}</div>
                <div class="stat-label">Trajets en attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['active_trips'] }}</div>
                <div class="stat-label">Trajets en cours</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['completed_today'] }}</div>
                <div class="stat-label">Terminés aujourd'hui</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $stats['total_users'] }}</div>
                <div class="stat-label">Utilisateurs totaux</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Trajets en attente -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-clock"></i> Trajets en Attente</h2>
                </div>
                <div class="widget-content">
                    @if ($pendingTrips->count() > 0)
                        <div class="table-responsive">
                            <table class="dashboard-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Conducteur</th>
                                        <th>Trajet</th>
                                        <th>Date/Heure</th>
                                        <th>Places</th>
                                        <th>Prix</th>
                                        <th>Véhicule</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($pendingTrips as $trip)
                                        <tr>
                                            <td>{{ $trip->id }}</td>
                                            <td>{{ $trip->conducteur->name ?? 'N/A' }}</td>
                                            <td>{{ $trip->lieu_depart }} → {{ $trip->lieu_arrivee }}</td>
                                            <td>{{ $trip->date_depart->format('d/m/Y') }}
                                                {{ $trip->heure_depart->format('H:i') }}</td>
                                            <td>{{ $trip->places_disponibles }}/{{ $trip->places_totales }}</td>
                                            <td>{{ $trip->prix }}€</td>
                                            <td>{{ $trip->voiture->marque ?? 'N/A' }}
                                                {{ $trip->voiture->modele ?? '' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p>Aucun trajet en attente.</p>
                    @endif
                </div>
            </div>

            <!-- Trajets en cours -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-route"></i> Trajets en Cours</h2>
                </div>
                <div class="widget-content">
                    @if ($activeTrips->count() > 0)
                        <div class="table-responsive">
                            <table class="dashboard-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Conducteur</th>
                                        <th>Trajet</th>
                                        <th>Heure départ</th>
                                        <th>Passagers</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($activeTrips as $trip)
                                        <tr>
                                            <td>{{ $trip->id }}</td>
                                            <td>{{ $trip->conducteur->name ?? 'N/A' }}</td>
                                            <td>{{ $trip->lieu_depart }} → {{ $trip->lieu_arrivee }}</td>
                                            <td>{{ $trip->heure_depart->format('H:i') }}</td>
                                            <td>{{ $trip->places_totales - $trip->places_disponibles }}/{{ $trip->places_totales }}
                                            </td>
                                            <td>
                                                <span class="status-badge status-active">En cours</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p>Aucun trajet en cours actuellement.</p>
                    @endif
                </div>
            </div>

            <!-- Problèmes signalés -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-exclamation-triangle"></i> Problèmes Signalés</h2>
                </div>
                <div class="widget-content">
                    @if (count($recentIssues) > 0)
                        <div class="issues-list">
                            @foreach ($recentIssues as $issue)
                                <div class="issue-item">
                                    <div class="issue-info">
                                        <h4>{{ $issue->title }}</h4>
                                        <p>{{ $issue->description }}</p>
                                        <small>Signalé par {{ $issue->user->name ?? 'N/A' }} -
                                            {{ $issue->created_at->diffForHumans() }}</small>
                                    </div>
                                    <div class="issue-actions">
                                        <button class="btn-base btn-primary">Traiter</button>
                                        <button class="btn-base btn-success">Résolu</button>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p>Aucun problème signalé récemment.</p>
                    @endif
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="dashboard-widget">
                <div class="widget-header">
                    <h2><i class="fas fa-tools"></i> Actions Rapides</h2>
                </div>
                <div class="widget-content">
                    <div class="action-buttons">
                        <a href="{{ route('dashboard_admin') }}" class="btn-base btn-primary">
                            <i class="fas fa-chart-bar"></i> Voir les statistiques détaillées
                        </a>
                        <button class="btn-base btn-warning" onclick="exportData()">
                            <i class="fas fa-download"></i> Exporter les données
                        </button>
                        <button class="btn-base btn-success" onclick="sendNotification()">
                            <i class="fas fa-bell"></i> Envoyer une notification
                        </button>
                        <button class="btn-base btn-danger" onclick="emergencyMode()">
                            <i class="fas fa-exclamation-circle"></i> Mode urgence
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('partials.footer')

    <script src="{{ asset('js/dashboard.js') }}"></script>
    <script>
        function exportData() {
            alert('Fonctionnalité d\'export en cours de développement');
        }

        function sendNotification() {
            alert('Fonctionnalité de notification en cours de développement');
        }

        function emergencyMode() {
            if (confirm('Activer le mode urgence ? Cela enverra des notifications à tous les administrateurs.')) {
                alert('Mode urgence activé');
            }
        }
    </script>
</body>

</html>
