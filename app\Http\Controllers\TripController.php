<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Covoiturage;
use App\Models\Voiture;
use App\Models\Confirmation;
use Carbon\Carbon;

class TripController extends Controller
{
    public function index()
    {
        $covoiturages = collect();
        
        return view('trips.index', compact('covoiturages'));
    }

    public function search(Request $request)
    {
        $validated = $request->validate([
            'lieu_depart' => 'required|string|max:100',
            'lieu_arrivee' => 'required|string|max:100',
            'date_depart' => 'required|date|after_or_equal:today',
            'nb_passagers' => 'required|integer|min:1|max:8'
        ]);

        $covoiturages = Covoiturage::with(['conducteur', 'voiture'])
            ->where('city_dep', 'LIKE', '%' . $validated['lieu_depart'] . '%')
            ->where('city_arr', 'LIKE', '%' . $validated['lieu_arrivee'] . '%')
            ->where('departure_date', $validated['date_depart'])
            ->where('n_tickets', '>=', $validated['nb_passagers'])
            ->where('cancelled', false)
            ->where('trip_completed', false)
            ->orderBy('departure_time', 'asc')
            ->get();

        return view('trips.index', compact('covoiturages'));
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        if (!in_array($user->role, ['Conducteur', 'Les deux'])) {
            return response()->json(['success' => false, 'message' => 'Vous devez être conducteur pour créer un covoiturage.'], 403);
        }

        $validated = $request->validate([
            'voiture_id' => 'required|exists:voiture,voiture_id',
            'lieu_depart' => 'required|string|max:100',
            'lieu_arrivee' => 'required|string|max:100',
            'date_depart' => 'required|date|after_or_equal:today',
            'heure_depart' => 'required|date_format:H:i',
            'prix' => 'required|numeric|min:2|max:100',
            'places_disponibles' => 'required|integer|min:1|max:8',
            'commentaire' => 'nullable|string|max:500',
            'ecologique' => 'boolean'
        ]);

        $vehicle = Voiture::findOrFail($validated['voiture_id']);
        
        if ($vehicle->user_id !== $user->user_id) {
            return response()->json(['success' => false, 'message' => 'Ce véhicule ne vous appartient pas.'], 403);
        }

        if ($validated['places_disponibles'] > $vehicle->n_place - 1) {
            return response()->json(['success' => false, 'message' => 'Nombre de places supérieur à la capacité du véhicule.'], 400);
        }

        DB::beginTransaction();
        try {
            $trip = new Covoiturage();
            $trip->user_id = $user->user_id;
            $trip->voiture_id = $validated['voiture_id'];
            $trip->departure_address = $validated['lieu_depart'];
            $trip->city_dep = $validated['lieu_depart'];
            $trip->arrival_address = $validated['lieu_arrivee'];
            $trip->city_arr = $validated['lieu_arrivee'];
            $trip->departure_date = $validated['date_depart'];
            $trip->arrival_date = $validated['date_depart'];
            $trip->departure_time = $validated['heure_depart'];
            $trip->arrival_time = $validated['heure_depart'];
            $trip->max_travel_time = '02:00:00';
            $trip->price = $validated['prix'];
            $trip->n_tickets = $validated['places_disponibles'];
            $trip->eco_travel = $validated['ecologique'] ?? false;
            $trip->trip_started = false;
            $trip->trip_completed = false;
            $trip->cancelled = false;
            $trip->save();

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Covoiturage créé avec succès.']);
            }

            return redirect()->back()->with('success', 'Covoiturage créé avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            
            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => 'Erreur lors de la création du covoiturage.'], 500);
            }
            
            return redirect()->back()->with('error', 'Erreur lors de la création du covoiturage.');
        }
    }

    public function show($id)
    {
        $covoiturage = Covoiturage::with(['conducteur', 'voiture'])->findOrFail($id);
        
        $confirmations = DB::table('confirmation')
            ->join('users', 'confirmation.user_id', '=', 'users.user_id')
            ->where('confirmation.covoit_id', $id)
            ->select('users.name', 'users.email', 'confirmation.statut')
            ->get();

        return view('trips.show', compact('covoiturage', 'confirmations'));
    }

    public function confirm($id)
    {
        $covoiturage = Covoiturage::with(['conducteur', 'voiture'])->findOrFail($id);
        return view('trips.confirm', compact('covoiturage'));
    }

    public function participate(Request $request, $id)
    {
        $user = Auth::user();
        $covoiturage = Covoiturage::findOrFail($id);

        if ($covoiturage->user_id === $user->user_id) {
            return redirect()->back()->with('error', 'Vous ne pouvez pas participer à votre propre covoiturage.');
        }

        if ($covoiturage->n_tickets <= 0) {
            return redirect()->back()->with('error', 'Plus de places disponibles pour ce covoiturage.');
        }

        $alreadyParticipating = DB::table('confirmation')
            ->where('covoit_id', $id)
            ->where('user_id', $user->user_id)
            ->exists();

        if ($alreadyParticipating) {
            return redirect()->back()->with('error', 'Vous participez déjà à ce covoiturage.');
        }

        if ($user->n_credit < $covoiturage->price) {
            return redirect()->back()->with('error', 'Crédits insuffisants pour participer à ce covoiturage.');
        }

        DB::beginTransaction();
        try {
            // Créer la confirmation
            $confirmation = new Confirmation();
            $confirmation->covoit_id = $id;
            $confirmation->user_id = $user->user_id;
            $confirmation->statut = 'En cours';
            $confirmation->n_conf = 1;
            $confirmation->save();

            // Décrémenter les places disponibles
            $covoiturage->n_tickets -= 1;
            $covoiturage->save();

            // Décrémenter les crédits du passager
            $user->n_credit -= $covoiturage->price;
            $user->save();

            // Incrémenter les crédits du conducteur
            $conducteur = $covoiturage->conducteur;
            $conducteur->n_credit += $covoiturage->price;
            $conducteur->save();

            DB::commit();

            return redirect()->back()->with('success', 'Participation confirmée avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Erreur lors de la confirmation de participation.');
        }
    }

    public function cancel($id)
    {
        $user = Auth::user();
        $covoiturage = Covoiturage::findOrFail($id);

        if ($covoiturage->user_id !== $user->user_id) {
            return redirect()->back()->with('error', 'Vous ne pouvez annuler que vos propres covoiturages.');
        }

        if ($covoiturage->trip_started || $covoiturage->trip_completed) {
            return redirect()->back()->with('error', 'Ce covoiturage ne peut plus être annulé.');
        }

        DB::beginTransaction();
        try {
            // Marquer comme annulé
            $covoiturage->cancelled = true;
            $covoiturage->save();

            // Rembourser les passagers
            $confirmations = DB::table('confirmation')
                ->where('covoit_id', $id)
                ->get();

            foreach ($confirmations as $confirmation) {
                $passager = DB::table('users')->where('user_id', $confirmation->user_id)->first();
                if ($passager) {
                    DB::table('users')
                        ->where('user_id', $confirmation->user_id)
                        ->increment('n_credit', $covoiturage->price);
                }
            }

            // Décrémenter les crédits du conducteur
            $user->n_credit -= ($covoiturage->price * $confirmations->count());
            $user->save();

            DB::commit();

            return redirect()->back()->with('success', 'Covoiturage annulé avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Erreur lors de l\'annulation du covoiturage.');
        }
    }

    public function start($id)
    {
        $user = Auth::user();
        $covoiturage = Covoiturage::findOrFail($id);

        if ($covoiturage->user_id !== $user->user_id) {
            return response()->json(['success' => false, 'message' => 'Action non autorisée.'], 403);
        }

        if ($covoiturage->trip_started || $covoiturage->cancelled) {
            return response()->json(['success' => false, 'message' => 'Ce covoiturage ne peut pas être démarré.'], 400);
        }

        $covoiturage->trip_started = true;
        $covoiturage->save();

        return response()->json(['success' => true, 'message' => 'Covoiturage démarré avec succès.']);
    }

    public function end($id)
    {
        $user = Auth::user();
        $covoiturage = Covoiturage::findOrFail($id);

        if ($covoiturage->user_id !== $user->user_id) {
            return response()->json(['success' => false, 'message' => 'Action non autorisée.'], 403);
        }

        if (!$covoiturage->trip_started || $covoiturage->trip_completed) {
            return response()->json(['success' => false, 'message' => 'Ce covoiturage ne peut pas être terminé.'], 400);
        }

        $covoiturage->trip_completed = true;
        $covoiturage->save();

        return response()->json(['success' => true, 'message' => 'Covoiturage terminé avec succès.']);
    }
}
