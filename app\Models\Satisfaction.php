<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Satisfaction extends Model
{
    use HasFactory;

    protected $table = 'satisfaction';
    protected $primaryKey = 'satisfaction_id';

    protected $fillable = [
        'user_id',
        'covoit_id',
        'feeling',
        'comment',
        'review',
        'note',
        'date',
    ];

    public $timestamps = false;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function covoiturage()
    {
        return $this->belongsTo(Covoiturage::class, 'covoit_id', 'covoit_id');
    }
}
