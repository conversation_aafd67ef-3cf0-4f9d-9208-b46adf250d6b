.confirm-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 40px 10px 40px;
}

.block-confirm-marg {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
    margin-bottom: 3rem;
}


.block-confirm {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.block-confirm-marg h2,
.block-confirm h2 {
    margin-bottom: 1rem;
}

.block-confirm-margbot {
    margin: 0 0 2rem 0;
}

.block-confirm-margbot h3 {
    margin-bottom: 1rem;
}

.trip-route-confirm {
    padding: 0 1rem 1rem 1rem;
}

.trip-details-arrival {
    justify-items: flex-end;
}

#modal-price {
    font-size: 1.6rem;
    font-weight: bolder;
    color: #2ecc71;
}

.price-route-confirm {
    text-align: center;
}

.price-route-confirm p {
    display: flex;
    flex-direction: column;
}

.adress-route-confirm {
    padding: 0 1rem 0 1rem;
}

.trip-details-header {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.trip-details-header .role-submit-btn {
    width: 100%;
    max-width: 200px;
    margin: 1rem 0 2rem 0;
    text-align: center;
    text-decoration: none;
}

.trip-grid-confirm {
    margin-top: 2rem;
}

.form-submit-confirm {
    display: flex;
    justify-content: center;
}

.next-confirm {
    max-width: 200px;
    margin: 2rem 0 1rem 0;
    text-align: center;
    font-size: larger;
}

.trip-info-confirm {
    padding: 0.6rem;
    text-align: center;
}

.driver-profile-confirm {
    display: flex;
    align-items: center;
    flex-direction: column;
}

.reviews-container-confirm .reviews-list {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 10px 0;
    gap: 15px;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f8f9fa;
}

.reviews-container-confirm .review-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    min-width: 250px;
    max-width: 300px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


@media (max-width: 620px) {
    h1, h2 {
        text-align: center;
    }
}


@media (max-width: 540px) {
    .confirm-container {
        padding: 40px 20px 10px 20px;
    }

    .trip-details-arrival {
        justify-items: flex-start;
        margin-top: 1rem;
    }

    .trip-route-container-flex {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .trip-route-details {
        flex-direction: column;
    }

    .route-arrow-details {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .adresse-route-confirm {
        justify-items: center;
        margin-bottom: 2.5rem;
    }

    .trip-addresses {
        display: flex;
        gap: 20px;
        flex-direction: column;
        align-items: center;
        margin-bottom: 2.5rem;
    }

    .address-group, .trip-addresses .address-group:nth-child(2) {
        text-align: center;
    }

    .address-group {
        flex-direction: column;
        align-items: center;
    }

    .block-confirm-margbot {
        text-align: center;
    }

}


/* modale */
.message-avert {
    font-size: 0.8rem;
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
    margin-top: 2rem;
}

.final-confirm {
    max-width: 200px;
    text-align: center;
    font-size: larger;
}

.bolb-trip-info-confirm {
    font-weight: bold;
}

#trip-cost {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2ecc71;
}

.credit-cost {
    font-size: x-large;
}
