<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('covoiturages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conducteur_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('voiture_id')->constrained('voitures')->onDelete('cascade');
            $table->string('lieu_depart', 100);
            $table->string('lieu_arrivee', 100);
            $table->date('date_depart');
            $table->time('heure_depart');
            $table->decimal('prix', 8, 2);
            $table->integer('places_disponibles');
            $table->integer('places_totales');
            $table->text('commentaire')->nullable();
            $table->boolean('ecologique')->default(false);
            $table->enum('statut', ['planifie', 'en_cours', 'termine', 'annule'])->default('planifie');
            $table->timestamps();
            
            // Index pour améliorer les performances des recherches
            $table->index(['lieu_depart', 'lieu_arrivee', 'date_depart']);
            $table->index(['conducteur_id', 'statut']);
            $table->index(['date_depart', 'statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('covoiturages');
    }
};
