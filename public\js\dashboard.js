// Dashboard JavaScript - Fonctionnalités pour les dashboards

// Gestion des modales
function openVehicleModal() {
    document.getElementById('vehicleModal').style.display = 'block';
}

function closeVehicleModal() {
    document.getElementById('vehicleModal').style.display = 'none';
}

function openTripModal() {
    document.getElementById('tripModal').style.display = 'block';
}

function closeTripModal() {
    document.getElementById('tripModal').style.display = 'none';
}

// Fermer les modales en cliquant à l'extérieur
window.onclick = function(event) {
    const vehicleModal = document.getElementById('vehicleModal');
    const tripModal = document.getElementById('tripModal');
    
    if (event.target === vehicleModal) {
        vehicleModal.style.display = 'none';
    }
    if (event.target === tripModal) {
        tripModal.style.display = 'none';
    }
}

// Gestion des véhicules
function editVehicle(vehicleId) {
    // Récupérer les données du véhicule et pré-remplir le formulaire
    fetch(`/vehicles/${vehicleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Pré-remplir le formulaire avec les données du véhicule
                document.querySelector('[name="marque"]').value = data.vehicle.marque;
                document.querySelector('[name="modele"]').value = data.vehicle.modele;
                document.querySelector('[name="immatriculation"]').value = data.vehicle.immatriculation;
                document.querySelector('[name="couleur"]').value = data.vehicle.couleur;
                document.querySelector('[name="nb_places"]').value = data.vehicle.nb_places;
                document.querySelector('[name="energie"]').value = data.vehicle.energie;
                document.querySelector('[name="annee"]').value = data.vehicle.annee;
                
                // Changer l'action du formulaire pour la mise à jour
                document.getElementById('vehicleForm').action = `/vehicles/${vehicleId}`;
                document.getElementById('vehicleForm').method = 'PUT';
                
                openVehicleModal();
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des données du véhicule');
        });
}

function deleteVehicle(vehicleId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
        // Vérifier d'abord si le véhicule est lié à des trajets
        fetch(`/vehicles/${vehicleId}/check-trips`)
            .then(response => response.json())
            .then(data => {
                if (data.hasTrips) {
                    alert('Impossible de supprimer ce véhicule car il est lié à des covoiturages.');
                    return;
                }
                
                // Supprimer le véhicule
                fetch(`/vehicles/${vehicleId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Erreur lors de la suppression');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la suppression du véhicule');
                });
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors de la vérification du véhicule');
            });
    }
}

// Gestion des trajets
function startTrip(tripId) {
    if (confirm('Êtes-vous sûr de vouloir démarrer ce trajet ?')) {
        fetch(`/trips/${tripId}/start`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors du démarrage du trajet');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du démarrage du trajet');
        });
    }
}

function endTrip(tripId) {
    if (confirm('Êtes-vous sûr de vouloir terminer ce trajet ?')) {
        fetch(`/trips/${tripId}/end`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la fin du trajet');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la fin du trajet');
        });
    }
}

function cancelTrip(tripId) {
    if (confirm('Êtes-vous sûr de vouloir annuler ce trajet ? Les passagers seront remboursés.')) {
        fetch(`/trips/${tripId}/cancel`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de l\'annulation du trajet');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'annulation du trajet');
        });
    }
}

// Gestion des rôles
document.addEventListener('DOMContentLoaded', function() {
    const roleOptions = document.querySelectorAll('.role-option');
    
    roleOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Retirer la classe selected de toutes les options
            roleOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Ajouter la classe selected à l'option cliquée
            this.classList.add('selected');
            
            // Cocher le radio button correspondant
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
        });
    });
});

// Gestion des formulaires AJAX
document.addEventListener('DOMContentLoaded', function() {
    const vehicleForm = document.getElementById('vehicleForm');
    
    if (vehicleForm) {
        vehicleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const method = this.method || 'POST';
            const action = this.action;
            
            fetch(action, {
                method: method,
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeVehicleModal();
                    location.reload();
                } else {
                    alert(data.message || 'Erreur lors de l\'enregistrement');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors de l\'enregistrement du véhicule');
            });
        });
    }
});

// Fonctions pour l'administration
function deleteUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
        fetch(`/admin/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la suppression de l\'utilisateur');
        });
    }
}

function changeUserRole(userId, newRole) {
    if (confirm(`Êtes-vous sûr de vouloir changer le rôle de cet utilisateur en "${newRole}" ?`)) {
        fetch(`/admin/users/${userId}/role`, {
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ role: newRole })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors du changement de rôle');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du changement de rôle');
        });
    }
}

function deleteTrip(tripId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce covoiturage ?')) {
        fetch(`/admin/trips/${tripId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la suppression du covoiturage');
        });
    }
}

function deleteVehicleAdmin(vehicleId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')) {
        fetch(`/admin/vehicles/${vehicleId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la suppression du véhicule');
        });
    }
}
