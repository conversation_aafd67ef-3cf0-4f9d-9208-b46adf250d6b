<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Voiture;
use App\Models\Covoiturage;

class DashboardController extends Controller
{
    public function users()
    {
        $user = Auth::user();

        $data = [
            'profile_photo' => null,
            'profile_photo_mime' => null,
            'vehicles' => [],
            'offeredTrips' => [],
            'reservations' => [],
            'passengerHistory' => [],
            'driverHistory' => [],
        ];

        if ($user->idphoto) {
            $data['profile_photo'] = base64_encode($user->idphoto);
            $data['profile_photo_mime'] = 'image/jpeg';
        }

        $vehicles = Voiture::where('user_id', $user->user_id)->get();
        $data['vehicles'] = $vehicles;

        $offeredTrips = Covoiturage::where('user_id', $user->user_id)
            ->where('cancelled', false)
            ->where('trip_completed', false)
            ->orderBy('departure_date', 'asc')
            ->get();
        $data['offeredTrips'] = $offeredTrips;

        $reservations = DB::table('confirmation')
            ->join('covoiturage', 'confirmation.covoit_id', '=', 'covoiturage.covoit_id')
            ->where('confirmation.user_id', $user->user_id)
            ->where('covoiturage.cancelled', false)
            ->where('covoiturage.trip_completed', false)
            ->select('covoiturage.*', 'confirmation.statut as reservation_status')
            ->orderBy('covoiturage.departure_date', 'asc')
            ->get();
        $data['reservations'] = $reservations;

        $passengerHistory = DB::table('confirmation')
            ->join('covoiturage', 'confirmation.covoit_id', '=', 'covoiturage.covoit_id')
            ->where('confirmation.user_id', $user->user_id)
            ->where('covoiturage.trip_completed', true)
            ->select('covoiturage.*', 'confirmation.statut as reservation_status')
            ->orderBy('covoiturage.departure_date', 'desc')
            ->get();
        $data['passengerHistory'] = $passengerHistory;

        $driverHistory = Covoiturage::where('user_id', $user->user_id)
            ->where('trip_completed', true)
            ->orderBy('departure_date', 'desc')
            ->get();
        $data['driverHistory'] = $driverHistory;

        return view('dashboard_users', $data);
    }

    public function admin()
    {
        $stats = [
            'total_users' => User::count(),
            'total_vehicles' => Voiture::count(),
            'total_trips' => Covoiturage::count(),
            'active_trips' => Covoiturage::where('statut', 'planifie')->count(),
            'completed_trips' => Covoiturage::where('statut', 'termine')->count(),
            'cancelled_trips' => Covoiturage::where('statut', 'annule')->count(),
        ];

        $recentUsers = User::orderBy('created_at', 'desc')->take(10)->get();
        $recentTrips = Covoiturage::with(['conducteur', 'voiture'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        $users = User::paginate(20);
        $vehicles = Voiture::with('user')->paginate(20);
        $trips = Covoiturage::with(['conducteur', 'voiture'])->paginate(20);

        return view('dashboard_admin', compact('stats', 'recentUsers', 'recentTrips', 'users', 'vehicles', 'trips'));
    }

    public function employe()
    {
        $stats = [
            'pending_trips' => Covoiturage::where('statut', 'planifie')->count(),
            'active_trips' => Covoiturage::where('statut', 'en_cours')->count(),
            'completed_today' => Covoiturage::where('statut', 'termine')
                ->whereDate('date_depart', today())
                ->count(),
            'total_users' => User::count(),
        ];

        $pendingTrips = Covoiturage::with(['conducteur', 'voiture'])
            ->where('statut', 'planifie')
            ->orderBy('date_depart', 'asc')
            ->take(15)
            ->get();

        $activeTrips = Covoiturage::with(['conducteur', 'voiture'])
            ->where('statut', 'en_cours')
            ->orderBy('heure_depart', 'asc')
            ->get();

        $recentIssues = [];

        return view('dashboard_employe', compact('stats', 'pendingTrips', 'activeTrips', 'recentIssues'));
    }

    public function updateRole(Request $request)
    {
        $request->validate([
            'role' => 'required|in:Passager,Conducteur,Les deux'
        ]);

        $user = Auth::user();
        DB::table('users')
            ->where('user_id', $user->user_id)
            ->update(['role' => $request->role]);

        return redirect()->back()->with('success', 'Rôle mis à jour avec succès.');
    }

    public function updateProfilePhoto(Request $request)
    {
        $request->validate([
            'profile_photo' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        $user = Auth::user();

        if ($request->hasFile('profile_photo')) {
            $photo = $request->file('profile_photo');
            $photoData = file_get_contents($photo->getRealPath());

            DB::table('users')
                ->where('user_id', $user->user_id)
                ->update(['idphoto' => $photoData]);
        }

        return redirect()->back()->with('success', 'Photo de profil mise à jour avec succès.');
    }

    public function deleteProfilePhoto()
    {
        $user = Auth::user();
        DB::table('users')
            ->where('user_id', $user->user_id)
            ->update(['idphoto' => null]);

        return redirect()->back()->with('success', 'Photo de profil supprimée avec succès.');
    }

    public function deleteUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        DB::beginTransaction();
        try {
            DB::table('confirmations')->where('passager_id', $id)->delete();

            $userTrips = Covoiturage::where('conducteur_id', $id)->get();
            foreach ($userTrips as $trip) {
                DB::table('confirmations')->where('covoiturage_id', $trip->id)->delete();
                $trip->delete();
            }

            Voiture::where('user_id', $id)->delete();

            $user->delete();

            DB::commit();

            return redirect()->back()->with('success', 'Utilisateur supprimé avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Erreur lors de la suppression de l\'utilisateur.');
        }
    }

    public function changeUserRole(Request $request, $id)
    {
        $request->validate([
            'role' => 'required|in:passager,conducteur,les_deux,admin,employe'
        ]);

        $user = User::findOrFail($id);
        $user->role = $request->role;
        $user->save();

        return redirect()->back()->with('success', 'Rôle de l\'utilisateur mis à jour avec succès.');
    }

    public function deleteTrip($id)
    {
        $trip = Covoiturage::findOrFail($id);

        DB::beginTransaction();
        try {
            DB::table('confirmations')->where('covoiturage_id', $id)->delete();
            $trip->delete();

            DB::commit();

            return redirect()->back()->with('success', 'Covoiturage supprimé avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Erreur lors de la suppression du covoiturage.');
        }
    }

    public function deleteVehicle($id)
    {
        $vehicle = Voiture::findOrFail($id);

        $hasTrips = Covoiturage::where('voiture_id', $id)->exists();

        if ($hasTrips) {
            return redirect()->back()->with('error', 'Impossible de supprimer ce véhicule car il est lié à des covoiturages.');
        }

        $vehicle->delete();

        return redirect()->back()->with('success', 'Véhicule supprimé avec succès.');
    }
}
