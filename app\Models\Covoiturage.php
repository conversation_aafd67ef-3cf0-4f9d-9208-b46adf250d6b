<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Covoiturage extends Model
{
    use HasFactory;

    protected $table = 'covoiturage';
    protected $primaryKey = 'covoit_id';

    protected $fillable = [
        'user_id',
        'voiture_id',
        'departure_address',
        'add_dep_address',
        'postal_code_dep',
        'city_dep',
        'arrival_address',
        'add_arr_address',
        'postal_code_arr',
        'city_arr',
        'departure_date',
        'arrival_date',
        'departure_time',
        'arrival_time',
        'max_travel_time',
        'price',
        'n_tickets',
        'eco_travel',
        'trip_started',
        'trip_completed',
        'cancelled',
    ];

    public $timestamps = false;

    protected $casts = [
        'departure_date' => 'date',
        'arrival_date' => 'date',
        'departure_time' => 'datetime:H:i',
        'arrival_time' => 'datetime:H:i',
        'max_travel_time' => 'datetime:H:i',
        'price' => 'integer',
        'n_tickets' => 'integer',
        'eco_travel' => 'boolean',
        'trip_started' => 'boolean',
        'trip_completed' => 'boolean',
        'cancelled' => 'boolean',
    ];

    // Accesseurs pour compatibilité avec les dashboards
    public function getConducteurIdAttribute()
    {
        return $this->user_id;
    }

    public function setConducteurIdAttribute($value)
    {
        $this->user_id = $value;
    }

    public function getLieuDepartAttribute()
    {
        return $this->departure_address . ', ' . $this->city_dep;
    }

    public function setLieuDepartAttribute($value)
    {
        $this->departure_address = $value;
    }

    public function getLieuArriveeAttribute()
    {
        return $this->arrival_address . ', ' . $this->city_arr;
    }

    public function setLieuArriveeAttribute($value)
    {
        $this->arrival_address = $value;
    }

    public function getDateDepartAttribute()
    {
        return $this->departure_date;
    }

    public function setDateDepartAttribute($value)
    {
        $this->departure_date = $value;
    }

    public function getHeureDepartAttribute()
    {
        return $this->departure_time;
    }

    public function setHeureDepartAttribute($value)
    {
        $this->departure_time = $value;
    }

    public function getPrixAttribute()
    {
        return $this->price;
    }

    public function setPrixAttribute($value)
    {
        $this->price = $value;
    }

    public function getPlacesDisponiblesAttribute()
    {
        return $this->n_tickets;
    }

    public function setPlacesDisponiblesAttribute($value)
    {
        $this->n_tickets = $value;
    }

    public function getPlacesTotalesAttribute()
    {
        return $this->n_tickets; // Dans l'ancienne structure, c'est le même champ
    }

    public function getCommentaireAttribute()
    {
        return ''; // Pas de commentaire dans l'ancienne structure
    }

    public function getEcologiqueAttribute()
    {
        return $this->eco_travel;
    }

    public function setEcologiqueAttribute($value)
    {
        $this->eco_travel = $value;
    }

    public function getStatutAttribute()
    {
        if ($this->cancelled) return 'annule';
        if ($this->trip_completed) return 'termine';
        if ($this->trip_started) return 'en_cours';
        return 'planifie';
    }

    public function getStatutFormateAttribute()
    {
        $statuts = [
            'planifie' => 'Planifié',
            'en_cours' => 'En cours',
            'termine' => 'Terminé',
            'annule' => 'Annulé'
        ];

        return $statuts[$this->statut] ?? $this->statut;
    }

    public function getDateDepartFormattedAttribute()
    {
        return $this->departure_date->format('d/m/Y');
    }

    public function getHeureDepartFormattedAttribute()
    {
        return $this->departure_time->format('H:i');
    }

    // Relations
    public function conducteur()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function voiture()
    {
        return $this->belongsTo(Voiture::class, 'voiture_id', 'voiture_id');
    }

    public function confirmations()
    {
        return $this->hasMany(Confirmation::class, 'covoit_id', 'covoit_id');
    }
}
