/* Styles pour l'authentification */
.auth-container {
    max-width: 600px;
    margin: 100px auto;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-container h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.auth-button {
    padding: 1rem;
    width: 100%;
    margin-top: 1rem;
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    color: #2c3e50;
}

.auth-links a {
    color: #2ecc71;
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Spécifique à l'inscription */
#password:invalid {
    border-color: #e74c3c;
}

#password:valid, #password-confirm:valid {
    border-color: #2ecc71;
}

#password-confirm.password-mismatch {
    border-color: #e74c3c;
}

li::marker {
    visibility: hidden;
}
