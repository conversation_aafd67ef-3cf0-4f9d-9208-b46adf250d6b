<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Voiture;
use App\Models\Covoiturage;
use Illuminate\Validation\Rule;

class VehicleController extends Controller
{
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'marque' => 'required|string|max:12',
            'modele' => 'required|string|max:24',
            'immatriculation' => ['required', 'string', 'max:10', Rule::unique('voiture', 'immat')],
            'couleur' => 'required|string|max:12',
            'nb_places' => 'required|integer|min:2|max:9',
            'energie' => 'required|in:Essence,Diesel/Gazole,Electrique,Hybride,GPL',
            'annee' => 'required|integer|min:1990|max:' . date('Y'),
        ]);

        DB::beginTransaction();
        try {
            $vehicle = new Voiture();
            $vehicle->user_id = $user->user_id;
            $vehicle->brand = $validated['marque'];
            $vehicle->model = $validated['modele'];
            $vehicle->immat = $validated['immatriculation'];
            $vehicle->color = $validated['couleur'];
            $vehicle->n_place = $validated['nb_places'];
            $vehicle->energie = $validated['energie'];
            $vehicle->date_first_immat = $validated['annee'] . '-01-01';
            $vehicle->save();

            // Mettre à jour le rôle si nécessaire
            if ($user->role === 'Passager') {
                DB::table('users')
                    ->where('user_id', $user->user_id)
                    ->update(['role' => 'Conducteur']);
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Véhicule ajouté avec succès.']);
            }

            return redirect()->back()->with('success', 'Véhicule ajouté avec succès.');
        } catch (\Exception $e) {
            DB::rollback();

            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => 'Erreur lors de l\'ajout du véhicule.'], 500);
            }

            return redirect()->back()->with('error', 'Erreur lors de l\'ajout du véhicule.');
        }
    }

    public function update(Request $request, $id)
    {
        $vehicle = Voiture::findOrFail($id);

        if ($vehicle->user_id !== Auth::id()) {
            return redirect()->back()->with('error', 'Action non autorisée.');
        }

        $validated = $request->validate([
            'marque' => 'required|string|max:50',
            'modele' => 'required|string|max:50',
            'immatriculation' => ['required', 'string', 'max:10', Rule::unique('voitures', 'immatriculation')->ignore($id)],
            'couleur' => 'required|string|max:30',
            'nb_places' => 'required|integer|min:2|max:9',
            'energie' => 'required|in:Essence,Diesel,Electrique,Hybride,GPL',
            'annee' => 'required|integer|min:1990|max:' . date('Y'),
        ]);

        $vehicle->update($validated);

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => 'Véhicule mis à jour avec succès.']);
        }

        return redirect()->back()->with('success', 'Véhicule mis à jour avec succès.');
    }

    public function destroy($id)
    {
        $vehicle = Voiture::findOrFail($id);

        if ($vehicle->user_id !== Auth::id()) {
            return redirect()->back()->with('error', 'Action non autorisée.');
        }

        $hasTrips = Covoiturage::where('voiture_id', $id)->exists();

        if ($hasTrips) {
            return redirect()->back()->with('error', 'Impossible de supprimer ce véhicule car il est lié à des covoiturages.');
        }

        DB::beginTransaction();
        try {
            $vehicle->delete();

            $user = Auth::user();
            $remainingVehicles = Voiture::where('user_id', $user->id)->count();

            if ($remainingVehicles === 0 && $user->role === 'conducteur') {
                $user->role = 'passager';
                $user->save();
            } elseif ($remainingVehicles === 0 && $user->role === 'les_deux') {
                $user->role = 'passager';
                $user->save();
            }

            DB::commit();

            return redirect()->back()->with('success', 'Véhicule supprimé avec succès.');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Erreur lors de la suppression du véhicule.');
        }
    }

    public function show($id)
    {
        $vehicle = Voiture::with('user')->findOrFail($id);

        if ($vehicle->user_id !== Auth::id()) {
            return redirect()->back()->with('error', 'Action non autorisée.');
        }

        $trips = Covoiturage::where('voiture_id', $id)
            ->orderBy('date_depart', 'desc')
            ->get();

        return view('vehicles.show', compact('vehicle', 'trips'));
    }

    public function checkTrips($id)
    {
        $vehicle = Voiture::findOrFail($id);

        if ($vehicle->user_id !== Auth::id()) {
            return response()->json(['error' => 'Action non autorisée.'], 403);
        }

        $hasTrips = Covoiturage::where('voiture_id', $id)->exists();

        return response()->json(['hasTrips' => $hasTrips]);
    }
}
