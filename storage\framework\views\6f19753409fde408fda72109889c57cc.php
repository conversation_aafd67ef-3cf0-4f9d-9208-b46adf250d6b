<header class="header">
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="<?php echo e(route('welcome')); ?>">
                    <img src="<?php echo e(asset('images/logo.png')); ?>" alt="EcoRide" class="logo-img">
                    <span class="logo-text">EcoRide</span>
                </a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="<?php echo e(route('welcome')); ?>" class="nav-link">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?php echo e(route('covoiturage')); ?>" class="nav-link">Rechercher</a>
                    </li>
                    <?php if(auth()->guard()->check()): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('dashboard_users')); ?>" class="nav-link">Mon Dashboard</a>
                        </li>
                        <?php if(Auth::user()->role === 'Admin'): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('dashboard_admin')); ?>" class="nav-link">Admin</a>
                            </li>
                        <?php endif; ?>
                        <?php if(in_array(Auth::user()->role, ['Admin', 'Employé'])): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('dashboard_employe')); ?>" class="nav-link">Employé</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link dropdown-toggle">
                                <i class="fas fa-user"></i> <?php echo e(Auth::user()->name); ?>

                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="<?php echo e(route('profile.edit')); ?>" class="dropdown-link">Mon Profil</a></li>
                                <li>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="dropdown-form">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="dropdown-link logout-btn">Déconnexion</button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('login')); ?>" class="nav-link">Connexion</a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('register')); ?>" class="nav-link nav-link-register">Inscription</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>
</header>

<style>
.header {
    background-color: #2c3e50;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.logo-img {
    height: 40px;
    margin-right: 0.5rem;
}

.logo-text {
    color: #2ecc71;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    margin-left: 2rem;
    position: relative;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
    display: block;
}

.nav-link:hover {
    color: #2ecc71;
}

.nav-link-register {
    background-color: #2ecc71;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link-register:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
}

.dropdown {
    position: relative;
}

.dropdown-toggle::after {
    content: ' ▼';
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    list-style: none;
    margin: 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #2c3e50;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-link:hover {
    background-color: #f8f9fa;
    color: #2ecc71;
}

.dropdown-form {
    margin: 0;
}

.logout-btn {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #2c3e50;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        padding: 2rem 0;
    }

    .nav-item {
        margin: 1rem 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background-color: #34495e;
        margin-top: 1rem;
    }

    .dropdown-link {
        color: white;
    }

    .dropdown-link:hover {
        background-color: #2c3e50;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
});
</script>
<?php /**PATH /var/www/html/resources/views/partials/header.blade.php ENDPATH**/ ?>